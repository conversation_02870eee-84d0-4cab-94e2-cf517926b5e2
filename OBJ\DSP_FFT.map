Component: ARM Compiler 5.06 update 7 (build 960) Tool: armlink [4d3601]

==============================================================================

Section Cross References

    main.o(.text) refers to misc.o(.text) for NVIC_PriorityGroupConfig
    main.o(.text) refers to delay.o(.text) for delay_init
    main.o(.text) refers to usart.o(.text) for uart_init
    main.o(.text) refers to led.o(.text) for LED_Init
    main.o(.text) refers to lcd.o(.text) for LCD_Init
    main.o(.text) refers to key.o(.text) for KEY_Init
    main.o(.text) refers to touch.o(.text) for TP_Init
    main.o(.text) refers to ui.o(.text) for UI_Init
    main.o(.text) refers to control.o(.text) for Control_Init
    main.o(.text) refers to ad9833.o(.text) for SPI1_Init
    main.o(.text) refers to mcp41010.o(.text) for AD9833_AmpSet_1
    main.o(.text) refers to dsp_process.o(.text) for DSP_Init
    main.o(.text) refers to adc_dma_timer.o(.text) for ADC_GPIO_Init
    main.o(.text) refers to noretval__2printf.o(.text) for __2printf
    main.o(.text) refers to stm32f4xx_dma.o(.text) for DMA_Cmd
    main.o(.text) refers to stm32f4xx_tim.o(.text) for TIM_Cmd
    stm32f4xx_it.o(.text) refers to stm32f4xx_tim.o(.text) for TIM_GetITStatus
    stm32f4xx_it.o(.text) refers to stm32f4xx_dma.o(.text) for DMA_GetITStatus
    stm32f4xx_it.o(.text) refers to dsp_process.o(.text) for Perform_Frequency_Sweep_Step_From_IRQ
    stm32f4xx_it.o(.text) refers to stm32f4xx_it.o(.data) for tim_irq_count
    stm32f4xx_it.o(.text) refers to main.o(.bss) for adc_dual_buf
    stm32f4xx_it.o(.text) refers to main.o(.data) for g_adc_capture_done
    system_stm32f4xx.o(.text) refers to system_stm32f4xx.o(.data) for SystemCoreClock
    led.o(.text) refers to stm32f4xx_rcc.o(.text) for RCC_AHB1PeriphClockCmd
    led.o(.text) refers to stm32f4xx_gpio.o(.text) for GPIO_Init
    key.o(.text) refers to stm32f4xx_rcc.o(.text) for RCC_AHB1PeriphClockCmd
    key.o(.text) refers to stm32f4xx_gpio.o(.text) for GPIO_Init
    key.o(.text) refers to delay.o(.text) for delay_ms
    key.o(.text) refers to key.o(.data) for key_up
    timer.o(.text) refers to stm32f4xx_rcc.o(.text) for RCC_APB1PeriphClockCmd
    timer.o(.text) refers to stm32f4xx_tim.o(.text) for TIM_TimeBaseInit
    timer.o(.text) refers to misc.o(.text) for NVIC_Init
    adc_dma_timer.o(.text) refers to stm32f4xx_rcc.o(.text) for RCC_AHB1PeriphClockCmd
    adc_dma_timer.o(.text) refers to stm32f4xx_gpio.o(.text) for GPIO_Init
    adc_dma_timer.o(.text) refers to stm32f4xx_tim.o(.text) for TIM_TimeBaseInit
    adc_dma_timer.o(.text) refers to misc.o(.text) for NVIC_Init
    adc_dma_timer.o(.text) refers to stm32f4xx_dma.o(.text) for DMA_DeInit
    adc_dma_timer.o(.text) refers to stm32f4xx_adc.o(.text) for ADC_CommonInit
    adc_dma_timer.o(.text) refers to main.o(.bss) for adc_dual_buf
    lcd.o(.text) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    lcd.o(.text) refers to _printf_x.o(.ARM.Collect$$_printf_percent$$0000000C) for _printf_x
    lcd.o(.text) refers to _printf_hex_int.o(.text) for _printf_longlong_hex
    lcd.o(.text) refers to delay.o(.text) for delay_us
    lcd.o(.text) refers to lcd.o(.bss) for lcddev
    lcd.o(.text) refers to dflt_clz.o(x$fpl$dfltu) for __aeabi_ui2d
    lcd.o(.text) refers to dmul.o(x$fpl$dmul) for __aeabi_dmul
    lcd.o(.text) refers to dfixu.o(x$fpl$dfixu) for __aeabi_d2uiz
    lcd.o(.text) refers to lcd.o(.data) for POINT_COLOR
    lcd.o(.text) refers to stm32f4xx_rcc.o(.text) for RCC_AHB1PeriphClockCmd
    lcd.o(.text) refers to stm32f4xx_gpio.o(.text) for GPIO_Init
    lcd.o(.text) refers to stm32f4xx_fsmc.o(.text) for FSMC_NORSRAMInit
    lcd.o(.text) refers to noretval__2printf.o(.text) for __2printf
    lcd.o(.text) refers to lcd.o(.constdata) for asc2_1206
    dac.o(.text) refers to stm32f4xx_rcc.o(.text) for RCC_AHB1PeriphClockCmd
    dac.o(.text) refers to stm32f4xx_gpio.o(.text) for GPIO_Init
    dac.o(.text) refers to stm32f4xx_dac.o(.text) for DAC_Init
    dac.o(.text) refers to dflt_clz.o(x$fpl$dfltu) for __aeabi_ui2d
    dac.o(.text) refers to ddiv.o(x$fpl$ddiv) for __aeabi_ddiv
    dac.o(.text) refers to dmul.o(x$fpl$dmul) for __aeabi_dmul
    dac.o(.text) refers to dfixu.o(x$fpl$dfixu) for __aeabi_d2uiz
    ad9833.o(.text) refers to stm32f4xx_rcc.o(.text) for RCC_AHB1PeriphClockCmd
    ad9833.o(.text) refers to stm32f4xx_gpio.o(.text) for GPIO_Init
    ad9833.o(.text) refers to ddiv.o(x$fpl$ddiv) for __aeabi_ddiv
    ad9833.o(.text) refers to dmul.o(x$fpl$dmul) for __aeabi_dmul
    ad9833.o(.text) refers to dfix.o(x$fpl$dfix) for __aeabi_d2iz
    ad9833.o(.text) refers to dleqf.o(x$fpl$dleqf) for __aeabi_cdcmple
    ad9833.o(.text) refers to delay.o(.text) for delay_ms
    ad9833.o(.text) refers to daddsub_clz.o(x$fpl$dadd) for __aeabi_dadd
    ad9833.o(.text) refers to daddsub_clz.o(x$fpl$dsub) for __aeabi_dsub
    ad9833.o(.text) refers to drleqf.o(x$fpl$drleqf) for __aeabi_cdrcmple
    ctiic.o(.text) refers to delay.o(.text) for delay_us
    ctiic.o(.text) refers to stm32f4xx_rcc.o(.text) for RCC_AHB1PeriphClockCmd
    ctiic.o(.text) refers to stm32f4xx_gpio.o(.text) for GPIO_Init
    ft5206.o(.text) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    ft5206.o(.text) refers to _printf_x.o(.ARM.Collect$$_printf_percent$$0000000C) for _printf_x
    ft5206.o(.text) refers to _printf_hex_int.o(.text) for _printf_longlong_hex
    ft5206.o(.text) refers to _printf_s.o(.ARM.Collect$$_printf_percent$$00000014) for _printf_s
    ft5206.o(.text) refers to _printf_str.o(.text) for _printf_str
    ft5206.o(.text) refers to ctiic.o(.text) for CT_IIC_Start
    ft5206.o(.text) refers to stm32f4xx_rcc.o(.text) for RCC_AHB1PeriphClockCmd
    ft5206.o(.text) refers to stm32f4xx_gpio.o(.text) for GPIO_Init
    ft5206.o(.text) refers to delay.o(.text) for delay_ms
    ft5206.o(.text) refers to noretval__2printf.o(.text) for __2printf
    ft5206.o(.text) refers to gt9147.o(.text) for GT9147_RD_Reg
    ft5206.o(.text) refers to strcmpv7m.o(.text) for strcmp
    ft5206.o(.text) refers to ft5206.o(.data) for CIP
    ft5206.o(.text) refers to touch.o(.data) for tp_dev
    ft5206.o(.text) refers to ft5206.o(.constdata) for GT911_TPX_TBL
    ft5206.o(.text) refers to lcd.o(.bss) for lcddev
    gt9147.o(.text) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    gt9147.o(.text) refers to _printf_s.o(.ARM.Collect$$_printf_percent$$00000014) for _printf_s
    gt9147.o(.text) refers to _printf_str.o(.text) for _printf_str
    gt9147.o(.text) refers to _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) for _printf_d
    gt9147.o(.text) refers to _printf_dec.o(.text) for _printf_int_dec
    gt9147.o(.text) refers to ctiic.o(.text) for CT_IIC_Start
    gt9147.o(.text) refers to stm32f4xx_rcc.o(.text) for RCC_AHB1PeriphClockCmd
    gt9147.o(.text) refers to stm32f4xx_gpio.o(.text) for GPIO_Init
    gt9147.o(.text) refers to delay.o(.text) for delay_ms
    gt9147.o(.text) refers to strcmpv7m.o(.text) for strcmp
    gt9147.o(.text) refers to noretval__2printf.o(.text) for __2printf
    gt9147.o(.text) refers to gt9147.o(.constdata) for GT9147_CFG_TBL
    gt9147.o(.text) refers to gt9147.o(.data) for t
    gt9147.o(.text) refers to touch.o(.data) for tp_dev
    gt9147.o(.text) refers to lcd.o(.bss) for lcddev
    ott2001a.o(.text) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    ott2001a.o(.text) refers to _printf_x.o(.ARM.Collect$$_printf_percent$$0000000C) for _printf_x
    ott2001a.o(.text) refers to _printf_hex_int.o(.text) for _printf_longlong_hex
    ott2001a.o(.text) refers to ctiic.o(.text) for CT_IIC_Start
    ott2001a.o(.text) refers to stm32f4xx_rcc.o(.text) for RCC_AHB1PeriphClockCmd
    ott2001a.o(.text) refers to stm32f4xx_gpio.o(.text) for GPIO_Init
    ott2001a.o(.text) refers to delay.o(.text) for delay_ms
    ott2001a.o(.text) refers to noretval__2printf.o(.text) for __2printf
    ott2001a.o(.text) refers to dflt_clz.o(x$fpl$dflt) for __aeabi_i2d
    ott2001a.o(.text) refers to dmul.o(x$fpl$dmul) for __aeabi_dmul
    ott2001a.o(.text) refers to dfixu.o(x$fpl$dfixu) for __aeabi_d2uiz
    ott2001a.o(.text) refers to daddsub_clz.o(x$fpl$drsb) for __aeabi_drsub
    ott2001a.o(.text) refers to ott2001a.o(.data) for t
    ott2001a.o(.text) refers to touch.o(.data) for tp_dev
    ott2001a.o(.text) refers to ott2001a.o(.constdata) for OTT_TPX_TBL
    touch.o(.text) refers to delay.o(.text) for delay_us
    touch.o(.text) refers to lcd.o(.text) for LCD_DrawLine
    touch.o(.text) refers to 24cxx.o(.text) for AT24CXX_WriteLenByte
    touch.o(.text) refers to touch.o(.data) for CMD_RDX
    touch.o(.text) refers to lcd.o(.data) for POINT_COLOR
    touch.o(.text) refers to dflt_clz.o(x$fpl$dfltu) for __aeabi_ui2d
    touch.o(.text) refers to sqrt.o(i.__hardfp_sqrt) for __hardfp_sqrt
    touch.o(.text) refers to dfixu.o(x$fpl$dfixu) for __aeabi_d2uiz
    touch.o(.text) refers to lcd.o(.bss) for lcddev
    touch.o(.text) refers to touch.o(.constdata) for TP_REMIND_MSG_TBL
    touch.o(.text) refers to f2d.o(x$fpl$f2d) for __aeabi_f2d
    touch.o(.text) refers to dleqf.o(x$fpl$dleqf) for __aeabi_cdcmple
    touch.o(.text) refers to drleqf.o(x$fpl$drleqf) for __aeabi_cdrcmple
    touch.o(.text) refers to dmul.o(x$fpl$dmul) for __aeabi_dmul
    touch.o(.text) refers to gt9147.o(.text) for GT9147_Init
    touch.o(.text) refers to ott2001a.o(.text) for OTT2001A_Init
    touch.o(.text) refers to ft5206.o(.text) for FT5206_Init
    touch.o(.text) refers to stm32f4xx_rcc.o(.text) for RCC_AHB1PeriphClockCmd
    touch.o(.text) refers to stm32f4xx_gpio.o(.text) for GPIO_Init
    touch.o(.constdata) refers to touch.o(.conststring) for .conststring
    touch.o(.data) refers to touch.o(.text) for TP_Init
    24cxx.o(.text) refers to myiic.o(.text) for IIC_Init
    24cxx.o(.text) refers to delay.o(.text) for delay_ms
    myiic.o(.text) refers to stm32f4xx_rcc.o(.text) for RCC_AHB1PeriphClockCmd
    myiic.o(.text) refers to stm32f4xx_gpio.o(.text) for GPIO_Init
    myiic.o(.text) refers to delay.o(.text) for delay_us
    ui.o(.text) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    ui.o(.text) refers to _printf_f.o(.ARM.Collect$$_printf_percent$$00000003) for _printf_f
    ui.o(.text) refers to printf1.o(x$fpl$printf1) for _printf_fp_dec
    ui.o(.text) refers to _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) for _printf_d
    ui.o(.text) refers to _printf_dec.o(.text) for _printf_int_dec
    ui.o(.text) refers to drleqf.o(x$fpl$drleqf) for __aeabi_cdrcmple
    ui.o(.text) refers to dleqf.o(x$fpl$dleqf) for __aeabi_cdcmple
    ui.o(.text) refers to dfixu.o(x$fpl$dfixu) for __aeabi_d2uiz
    ui.o(.text) refers to search.o(.text) for Search_Get_Best_Amp
    ui.o(.text) refers to ad9833.o(.text) for AD9833_1_WaveSeting
    ui.o(.text) refers to lcd.o(.text) for LCD_Fill
    ui.o(.text) refers to noretval__2sprintf.o(.text) for __2sprintf
    ui.o(.text) refers to ui.o(.data) for control_mode
    ui.o(.text) refers to lcd.o(.data) for BACK_COLOR
    ui.o(.text) refers to lcd.o(.bss) for lcddev
    ui.o(.text) refers to ui.o(.bss) for btn_freq_p
    ui.o(.text) refers to touch.o(.text) for TP_Scan
    ui.o(.text) refers to dsp_process.o(.text) for Perform_Frequency_Sweep_Start
    ui.o(.text) refers to strcat.o(.text) for strcat
    ui.o(.text) refers to f2d.o(x$fpl$f2d) for __aeabi_f2d
    ui.o(.text) refers to ui.o(.constdata) for freq_steps
    ui.o(.text) refers to touch.o(.data) for tp_dev
    ui.o(.text) refers to daddsub_clz.o(x$fpl$dadd) for __aeabi_dadd
    ui.o(.text) refers to daddsub_clz.o(x$fpl$dsub) for __aeabi_dsub
    ui.o(.text) refers to delay.o(.text) for delay_ms
    ui.o(.text) refers to control.o(.text) for Control_Get_Mode
    control.o(.text) refers to key.o(.text) for KEY_Scan
    control.o(.text) refers to ui.o(.text) for UI_Mode_Switch
    control.o(.text) refers to control.o(.data) for current_mode
    filter.o(.text) refers to f2d.o(x$fpl$f2d) for __aeabi_f2d
    filter.o(.text) refers to dmul.o(x$fpl$dmul) for __aeabi_dmul
    filter.o(.text) refers to ddiv.o(x$fpl$ddiv) for __aeabi_ddiv
    filter.o(.text) refers to tan.o(i.__hardfp_tan) for __hardfp_tan
    filter.o(.text) refers to daddsub_clz.o(x$fpl$dadd) for __aeabi_dadd
    filter.o(.text) refers to d2f.o(x$fpl$d2f) for __aeabi_d2f
    filter.o(.text) refers to daddsub_clz.o(x$fpl$dsub) for __aeabi_dsub
    search.o(.text) refers to search.o(.constdata) for required_vin_for_1V_table
    dsp_process.o(.text) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    dsp_process.o(.text) refers to _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) for _printf_d
    dsp_process.o(.text) refers to _printf_dec.o(.text) for _printf_int_dec
    dsp_process.o(.text) refers to arm_cfft_radix4_init_f32.o(.text) for arm_cfft_radix4_init_f32
    dsp_process.o(.text) refers to noretval__2printf.o(.text) for __2printf
    dsp_process.o(.text) refers to rt_memclr_w.o(.text) for __aeabi_memclr4
    dsp_process.o(.text) refers to stm32f4xx_tim.o(.text) for TIM_Cmd
    dsp_process.o(.text) refers to stm32f4xx_dma.o(.text) for DMA_Cmd
    dsp_process.o(.text) refers to rt_memclr.o(.text) for __aeabi_memclr
    dsp_process.o(.text) refers to ad9833.o(.text) for AD9833_1_WaveSeting
    dsp_process.o(.text) refers to delay.o(.text) for delay_ms
    dsp_process.o(.text) refers to arm_cfft_radix4_f32.o(.text) for arm_cfft_radix4_f32
    dsp_process.o(.text) refers to dflt_clz.o(x$fpl$dfltu) for __aeabi_ui2d
    dsp_process.o(.text) refers to dsp_process.o(.bss) for fft_inst
    dsp_process.o(.text) refers to dsp_process.o(.data) for sweep_index
    dsp_process.o(.text) refers to main.o(.bss) for gain_results
    delay.o(.text) refers to misc.o(.text) for SysTick_CLKSourceConfig
    delay.o(.text) refers to delay.o(.data) for fac_us
    usart.o(.rev16_text) refers (Special) to use_no_semi_2.o(.text) for __use_no_semihosting
    usart.o(.revsh_text) refers (Special) to use_no_semi_2.o(.text) for __use_no_semihosting
    usart.o(.text) refers (Special) to use_no_semi_2.o(.text) for __use_no_semihosting
    usart.o(.text) refers to stm32f4xx_rcc.o(.text) for RCC_AHB1PeriphClockCmd
    usart.o(.text) refers to stm32f4xx_gpio.o(.text) for GPIO_PinAFConfig
    usart.o(.text) refers to stm32f4xx_usart.o(.text) for USART_Init
    usart.o(.text) refers to misc.o(.text) for NVIC_Init
    usart.o(.text) refers to usart.o(.data) for USART_RX_STA
    usart.o(.text) refers to usart.o(.bss) for USART_RX_BUF
    usart.o(.bss) refers (Special) to use_no_semi_2.o(.text) for __use_no_semihosting
    usart.o(.data) refers (Special) to use_no_semi_2.o(.text) for __use_no_semihosting
    startup_stm32f40_41xxx.o(STACK) refers (Special) to heapauxi.o(.text) for __use_two_region_memory
    startup_stm32f40_41xxx.o(HEAP) refers (Special) to heapauxi.o(.text) for __use_two_region_memory
    startup_stm32f40_41xxx.o(RESET) refers (Special) to heapauxi.o(.text) for __use_two_region_memory
    startup_stm32f40_41xxx.o(RESET) refers to startup_stm32f40_41xxx.o(STACK) for __initial_sp
    startup_stm32f40_41xxx.o(RESET) refers to startup_stm32f40_41xxx.o(.text) for Reset_Handler
    startup_stm32f40_41xxx.o(RESET) refers to stm32f4xx_it.o(.text) for NMI_Handler
    startup_stm32f40_41xxx.o(RESET) refers to timer.o(.text) for TIM3_IRQHandler
    startup_stm32f40_41xxx.o(RESET) refers to usart.o(.text) for USART1_IRQHandler
    startup_stm32f40_41xxx.o(.text) refers (Special) to heapauxi.o(.text) for __use_two_region_memory
    startup_stm32f40_41xxx.o(.text) refers to system_stm32f4xx.o(.text) for SystemInit
    startup_stm32f40_41xxx.o(.text) refers to __main.o(!!!main) for __main
    startup_stm32f40_41xxx.o(.text) refers to startup_stm32f40_41xxx.o(HEAP) for Heap_Mem
    startup_stm32f40_41xxx.o(.text) refers to startup_stm32f40_41xxx.o(STACK) for Stack_Mem
    stm32f4xx_gpio.o(.text) refers to stm32f4xx_rcc.o(.text) for RCC_AHB1PeriphResetCmd
    stm32f4xx_fsmc.o(.text) refers to stm32f4xx_fsmc.o(.constdata) for FSMC_DefaultTimingStruct
    stm32f4xx_rcc.o(.text) refers to stm32f4xx_rcc.o(.data) for APBAHBPrescTable
    stm32f4xx_syscfg.o(.text) refers to stm32f4xx_rcc.o(.text) for RCC_APB2PeriphResetCmd
    stm32f4xx_usart.o(.text) refers to stm32f4xx_rcc.o(.text) for RCC_APB2PeriphResetCmd
    stm32f4xx_tim.o(.text) refers to stm32f4xx_rcc.o(.text) for RCC_APB2PeriphResetCmd
    stm32f4xx_adc.o(.text) refers to stm32f4xx_rcc.o(.text) for RCC_APB2PeriphResetCmd
    stm32f4xx_dac.o(.text) refers to stm32f4xx_rcc.o(.text) for RCC_APB1PeriphResetCmd
    arm_cfft_radix4_f32.o(.text) refers to arm_bitreversal.o(.text) for arm_bitreversal_f32
    arm_cfft_radix4_init_f32.o(.text) refers to arm_common_tables.o(.constdata) for twiddleCoef_4096
    arm_cfft_radix4_init_f32.o(.text) refers to arm_common_tables.o(.constdata) for armBitRevTable
    use_no_semi_2.o(.text) refers (Special) to use_no_semi.o(.text) for __use_no_semihosting_swi
    __2printf.o(.text) refers to _printf_char_file.o(.text) for _printf_char_file
    __2printf.o(.text) refers to usart.o(.data) for __stdout
    __2sprintf.o(.text) refers to _printf_char_common.o(.text) for _printf_char_common
    __2sprintf.o(.text) refers to _sputc.o(.text) for _sputc
    noretval__2printf.o(.text) refers to _printf_char_file.o(.text) for _printf_char_file
    noretval__2printf.o(.text) refers to usart.o(.data) for __stdout
    noretval__2sprintf.o(.text) refers to _printf_char_common.o(.text) for _printf_char_common
    noretval__2sprintf.o(.text) refers to _sputc.o(.text) for _sputc
    __printf.o(.text) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    _printf_str.o(.text) refers (Special) to _printf_char.o(.text) for _printf_cs_common
    _printf_dec.o(.text) refers to _printf_intcommon.o(.text) for _printf_int_common
    _printf_hex_ll.o(.text) refers to _printf_intcommon.o(.text) for _printf_int_common
    _printf_hex_ll.o(.text) refers to _printf_hex_ll.o(.constdata) for .constdata
    _printf_hex_int.o(.text) refers to _printf_intcommon.o(.text) for _printf_int_common
    _printf_hex_int.o(.text) refers to _printf_hex_int.o(.constdata) for .constdata
    _printf_hex_int_ll.o(.text) refers to _printf_intcommon.o(.text) for _printf_int_common
    _printf_hex_int_ll.o(.text) refers to _printf_hex_int_ll.o(.constdata) for .constdata
    _printf_hex_ptr.o(.text) refers to _printf_intcommon.o(.text) for _printf_int_common
    _printf_hex_ptr.o(.text) refers to _printf_hex_ptr.o(.constdata) for .constdata
    _printf_hex_int_ptr.o(.text) refers to _printf_intcommon.o(.text) for _printf_int_common
    _printf_hex_int_ptr.o(.text) refers to _printf_hex_int_ptr.o(.constdata) for .constdata
    _printf_hex_ll_ptr.o(.text) refers to _printf_intcommon.o(.text) for _printf_int_common
    _printf_hex_ll_ptr.o(.text) refers to _printf_hex_ll_ptr.o(.constdata) for .constdata
    _printf_hex_int_ll_ptr.o(.text) refers to _printf_intcommon.o(.text) for _printf_int_common
    _printf_hex_int_ll_ptr.o(.text) refers to _printf_hex_int_ll_ptr.o(.constdata) for .constdata
    __printf_flags.o(.text) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    __printf_flags.o(.text) refers to __printf_flags.o(.constdata) for .constdata
    __printf_ss.o(.text) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    __printf_flags_ss.o(.text) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    __printf_flags_ss.o(.text) refers to __printf_flags_ss.o(.constdata) for .constdata
    __printf_wp.o(.text) refers to __printf_wp.o(i._is_digit) for _is_digit
    __printf_wp.o(.text) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    __printf_flags_wp.o(.text) refers to __printf_wp.o(i._is_digit) for _is_digit
    __printf_flags_wp.o(.text) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    __printf_flags_wp.o(.text) refers to __printf_flags_wp.o(.constdata) for .constdata
    __printf_ss_wp.o(.text) refers to __printf_wp.o(i._is_digit) for _is_digit
    __printf_ss_wp.o(.text) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    __printf_flags_ss_wp.o(.text) refers to __printf_wp.o(i._is_digit) for _is_digit
    __printf_flags_ss_wp.o(.text) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    __printf_flags_ss_wp.o(.text) refers to __printf_flags_ss_wp.o(.constdata) for .constdata
    _printf_s.o(.ARM.Collect$$_printf_percent$$00000014) refers (Weak) to _printf_char.o(.text) for _printf_string
    _printf_x.o(.ARM.Collect$$_printf_percent$$0000000C) refers (Weak) to _printf_hex_int.o(.text) for _printf_int_hex
    _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) refers (Weak) to _printf_dec.o(.text) for _printf_int_dec
    _printf_f.o(.ARM.Collect$$_printf_percent$$00000003) refers (Weak) to printf1.o(x$fpl$printf1) for _printf_fp_dec
    _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) refers (Special) to _printf_percent_end.o(.ARM.Collect$$_printf_percent$$00000017) for _printf_percent_end
    rt_memclr.o(.text) refers to rt_memclr_w.o(.text) for _memset_w
    __main.o(!!!main) refers to __rtentry.o(.ARM.Collect$$rtentry$$00000000) for __rt_entry
    d2f.o(x$fpl$d2f) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    d2f.o(x$fpl$d2f) refers to fretinf.o(x$fpl$fretinf) for __fpl_fretinf
    d2f.o(x$fpl$d2f) refers to dnaninf.o(x$fpl$dnaninf) for __fpl_dnaninf
    daddsub_clz.o(x$fpl$dadd) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    daddsub_clz.o(x$fpl$dadd) refers to daddsub_clz.o(x$fpl$dsub) for _dsub1
    daddsub_clz.o(x$fpl$dadd) refers to dretinf.o(x$fpl$dretinf) for __fpl_dretinf
    daddsub_clz.o(x$fpl$dadd) refers to dnaninf.o(x$fpl$dnaninf) for __fpl_dnaninf
    daddsub_clz.o(x$fpl$drsb) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    daddsub_clz.o(x$fpl$drsb) refers to daddsub_clz.o(x$fpl$dadd) for _dadd1
    daddsub_clz.o(x$fpl$drsb) refers to daddsub_clz.o(x$fpl$dsub) for _dsub1
    daddsub_clz.o(x$fpl$dsub) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    daddsub_clz.o(x$fpl$dsub) refers to daddsub_clz.o(x$fpl$dadd) for _dadd1
    daddsub_clz.o(x$fpl$dsub) refers to dnaninf.o(x$fpl$dnaninf) for __fpl_dnaninf
    ddiv.o(x$fpl$drdiv) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    ddiv.o(x$fpl$drdiv) refers to ddiv.o(x$fpl$ddiv) for ddiv_entry
    ddiv.o(x$fpl$ddiv) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    ddiv.o(x$fpl$ddiv) refers to dretinf.o(x$fpl$dretinf) for __fpl_dretinf
    ddiv.o(x$fpl$ddiv) refers to dnaninf.o(x$fpl$dnaninf) for __fpl_dnaninf
    dfix.o(x$fpl$dfix) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    dfix.o(x$fpl$dfix) refers to dnaninf.o(x$fpl$dnaninf) for __fpl_dnaninf
    dfix.o(x$fpl$dfixr) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    dfix.o(x$fpl$dfixr) refers to dnaninf.o(x$fpl$dnaninf) for __fpl_dnaninf
    dfixu.o(x$fpl$dfixu) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    dfixu.o(x$fpl$dfixu) refers to dnaninf.o(x$fpl$dnaninf) for __fpl_dnaninf
    dfixu.o(x$fpl$dfixur) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    dfixu.o(x$fpl$dfixur) refers to dnaninf.o(x$fpl$dnaninf) for __fpl_dnaninf
    dflt_clz.o(x$fpl$dfltu) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    dflt_clz.o(x$fpl$dflt) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    dflt_clz.o(x$fpl$dfltn) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    dleqf.o(x$fpl$dleqf) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    dleqf.o(x$fpl$dleqf) refers to dnaninf.o(x$fpl$dnaninf) for __fpl_dnaninf
    dleqf.o(x$fpl$dleqf) refers to dcmpi.o(x$fpl$dcmpinf) for __fpl_dcmp_Inf
    dmul.o(x$fpl$dmul) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    dmul.o(x$fpl$dmul) refers to dretinf.o(x$fpl$dretinf) for __fpl_dretinf
    dmul.o(x$fpl$dmul) refers to dnaninf.o(x$fpl$dnaninf) for __fpl_dnaninf
    drleqf.o(x$fpl$drleqf) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    drleqf.o(x$fpl$drleqf) refers to dleqf.o(x$fpl$dleqf) for __fpl_dcmple_InfNaN
    f2d.o(x$fpl$f2d) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    f2d.o(x$fpl$f2d) refers to fnaninf.o(x$fpl$fnaninf) for __fpl_fnaninf
    f2d.o(x$fpl$f2d) refers to dretinf.o(x$fpl$dretinf) for __fpl_dretinf
    printf1.o(x$fpl$printf1) refers to _printf_fp_dec.o(.text) for _printf_fp_dec_real
    sqrt.o(i.__hardfp_sqrt) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    sqrt.o(i.__hardfp_sqrt) refers to dsqrt_umaal.o(x$fpl$dsqrt) for _dsqrt
    sqrt.o(i.__hardfp_sqrt) refers to _rserrno.o(.text) for __set_errno
    sqrt.o(i.__softfp_sqrt) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    sqrt.o(i.__softfp_sqrt) refers to dsqrt_umaal.o(x$fpl$dsqrt) for _dsqrt
    sqrt.o(i.__softfp_sqrt) refers to _rserrno.o(.text) for __set_errno
    sqrt.o(i.sqrt) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    sqrt.o(i.sqrt) refers to dsqrt_umaal.o(x$fpl$dsqrt) for _dsqrt
    sqrt.o(i.sqrt) refers to _rserrno.o(.text) for __set_errno
    sqrt_x.o(i.____hardfp_sqrt$lsc) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    sqrt_x.o(i.____hardfp_sqrt$lsc) refers to dleqf.o(x$fpl$dleqf) for __aeabi_cdcmple
    sqrt_x.o(i.____hardfp_sqrt$lsc) refers to _rserrno.o(.text) for __set_errno
    sqrt_x.o(i.____hardfp_sqrt$lsc) refers to dsqrt_umaal.o(x$fpl$dsqrt) for _dsqrt
    sqrt_x.o(i.____softfp_sqrt$lsc) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    sqrt_x.o(i.____softfp_sqrt$lsc) refers to dleqf.o(x$fpl$dleqf) for __aeabi_cdcmple
    sqrt_x.o(i.____softfp_sqrt$lsc) refers to _rserrno.o(.text) for __set_errno
    sqrt_x.o(i.____softfp_sqrt$lsc) refers to dsqrt_umaal.o(x$fpl$dsqrt) for _dsqrt
    sqrt_x.o(i.__sqrt$lsc) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    sqrt_x.o(i.__sqrt$lsc) refers to dleqf.o(x$fpl$dleqf) for __aeabi_cdcmple
    sqrt_x.o(i.__sqrt$lsc) refers to _rserrno.o(.text) for __set_errno
    sqrt_x.o(i.__sqrt$lsc) refers to dsqrt_umaal.o(x$fpl$dsqrt) for _dsqrt
    tan.o(i.__hardfp_tan) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    tan.o(i.__hardfp_tan) refers to _rserrno.o(.text) for __set_errno
    tan.o(i.__hardfp_tan) refers to dunder.o(i.__mathlib_dbl_invalid) for __mathlib_dbl_invalid
    tan.o(i.__hardfp_tan) refers to dunder.o(i.__mathlib_dbl_infnan) for __mathlib_dbl_infnan
    tan.o(i.__hardfp_tan) refers to rred.o(i.__ieee754_rem_pio2) for __ieee754_rem_pio2
    tan.o(i.__hardfp_tan) refers to tan_i.o(i.__kernel_tan) for __kernel_tan
    tan.o(i.__softfp_tan) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    tan.o(i.__softfp_tan) refers to _rserrno.o(.text) for __set_errno
    tan.o(i.__softfp_tan) refers to dunder.o(i.__mathlib_dbl_invalid) for __mathlib_dbl_invalid
    tan.o(i.__softfp_tan) refers to dunder.o(i.__mathlib_dbl_infnan) for __mathlib_dbl_infnan
    tan.o(i.__softfp_tan) refers to rred.o(i.__ieee754_rem_pio2) for __ieee754_rem_pio2
    tan.o(i.__softfp_tan) refers to tan_i.o(i.__kernel_tan) for __kernel_tan
    tan.o(i.tan) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    tan.o(i.tan) refers to _rserrno.o(.text) for __set_errno
    tan.o(i.tan) refers to dunder.o(i.__mathlib_dbl_invalid) for __mathlib_dbl_invalid
    tan.o(i.tan) refers to dunder.o(i.__mathlib_dbl_infnan) for __mathlib_dbl_infnan
    tan.o(i.tan) refers to rred.o(i.__ieee754_rem_pio2) for __ieee754_rem_pio2
    tan.o(i.tan) refers to tan_i.o(i.__kernel_tan) for __kernel_tan
    tan_x.o(i.____hardfp_tan$lsc) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    tan_x.o(i.____hardfp_tan$lsc) refers to _rserrno.o(.text) for __set_errno
    tan_x.o(i.____hardfp_tan$lsc) refers to dunder.o(i.__mathlib_dbl_infnan) for __mathlib_dbl_infnan
    tan_x.o(i.____hardfp_tan$lsc) refers to rred.o(i.__ieee754_rem_pio2) for __ieee754_rem_pio2
    tan_x.o(i.____hardfp_tan$lsc) refers to tan_i.o(i.__kernel_tan) for __kernel_tan
    tan_x.o(i.____softfp_tan$lsc) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    tan_x.o(i.____softfp_tan$lsc) refers to _rserrno.o(.text) for __set_errno
    tan_x.o(i.____softfp_tan$lsc) refers to dunder.o(i.__mathlib_dbl_infnan) for __mathlib_dbl_infnan
    tan_x.o(i.____softfp_tan$lsc) refers to rred.o(i.__ieee754_rem_pio2) for __ieee754_rem_pio2
    tan_x.o(i.____softfp_tan$lsc) refers to tan_i.o(i.__kernel_tan) for __kernel_tan
    tan_x.o(i.__tan$lsc) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    tan_x.o(i.__tan$lsc) refers to _rserrno.o(.text) for __set_errno
    tan_x.o(i.__tan$lsc) refers to dunder.o(i.__mathlib_dbl_infnan) for __mathlib_dbl_infnan
    tan_x.o(i.__tan$lsc) refers to rred.o(i.__ieee754_rem_pio2) for __ieee754_rem_pio2
    tan_x.o(i.__tan$lsc) refers to tan_i.o(i.__kernel_tan) for __kernel_tan
    __rtentry.o(.ARM.Collect$$rtentry$$00000000) refers (Special) to __rtentry2.o(.ARM.Collect$$rtentry$$0000000A) for __rt_entry_li
    __rtentry.o(.ARM.Collect$$rtentry$$00000000) refers (Special) to __rtentry2.o(.ARM.Collect$$rtentry$$0000000D) for __rt_entry_main
    __rtentry.o(.ARM.Collect$$rtentry$$00000000) refers (Special) to __rtentry2.o(.ARM.Collect$$rtentry$$0000000C) for __rt_entry_postli_1
    __rtentry.o(.ARM.Collect$$rtentry$$00000000) refers (Special) to __rtentry2.o(.ARM.Collect$$rtentry$$00000009) for __rt_entry_postsh_1
    __rtentry.o(.ARM.Collect$$rtentry$$00000000) refers (Special) to __rtentry2.o(.ARM.Collect$$rtentry$$00000002) for __rt_entry_presh_1
    __rtentry.o(.ARM.Collect$$rtentry$$00000000) refers (Special) to __rtentry4.o(.ARM.Collect$$rtentry$$00000004) for __rt_entry_sh
    _rserrno.o(.text) refers to rt_errno_addr_intlibspace.o(.text) for __aeabi_errno_addr
    _printf_fp_dec.o(.text) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    _printf_fp_dec.o(.text) refers (Special) to lc_numeric_c.o(locale$$code) for _get_lc_numeric
    _printf_fp_dec.o(.text) refers to bigflt0.o(.text) for _btod_etento
    _printf_fp_dec.o(.text) refers to btod.o(CL$$btod_d2e) for _btod_d2e
    _printf_fp_dec.o(.text) refers to btod.o(CL$$btod_ediv) for _btod_ediv
    _printf_fp_dec.o(.text) refers to btod.o(CL$$btod_emul) for _btod_emul
    _printf_fp_dec.o(.text) refers to lludiv10.o(.text) for _ll_udiv10
    _printf_fp_dec.o(.text) refers to fpclassify.o(i.__ARM_fpclassify) for __ARM_fpclassify
    _printf_fp_dec.o(.text) refers to _printf_fp_infnan.o(.text) for _printf_fp_infnan
    _printf_fp_dec.o(.text) refers to rt_locale_intlibspace.o(.text) for __rt_locale
    _printf_char_common.o(.text) refers to __printf_wp.o(.text) for __printf
    _printf_char.o(.text) refers (Weak) to _printf_str.o(.text) for _printf_str
    _printf_char_file.o(.text) refers to _printf_char_common.o(.text) for _printf_char_common
    _printf_char_file.o(.text) refers to ferror.o(.text) for ferror
    _printf_char_file.o(.text) refers to usart.o(.text) for fputc
    dcmpi.o(x$fpl$dcmpinf) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    dnaninf.o(x$fpl$dnaninf) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    dretinf.o(x$fpl$dretinf) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    dsqrt_umaal.o(x$fpl$dsqrt) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    dsqrt_umaal.o(x$fpl$dsqrt) refers to dnaninf.o(x$fpl$dnaninf) for __fpl_dnaninf
    fnaninf.o(x$fpl$fnaninf) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    fretinf.o(x$fpl$fretinf) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    dunder.o(i.__mathlib_dbl_divzero) refers to ddiv.o(x$fpl$ddiv) for __aeabi_ddiv
    dunder.o(i.__mathlib_dbl_infnan) refers to daddsub_clz.o(x$fpl$dadd) for __aeabi_dadd
    dunder.o(i.__mathlib_dbl_infnan2) refers to daddsub_clz.o(x$fpl$dadd) for __aeabi_dadd
    dunder.o(i.__mathlib_dbl_invalid) refers to ddiv.o(x$fpl$ddiv) for __aeabi_ddiv
    dunder.o(i.__mathlib_dbl_overflow) refers to dmul.o(x$fpl$dmul) for __aeabi_dmul
    dunder.o(i.__mathlib_dbl_posinfnan) refers to dmul.o(x$fpl$dmul) for __aeabi_dmul
    dunder.o(i.__mathlib_dbl_underflow) refers to dmul.o(x$fpl$dmul) for __aeabi_dmul
    rred.o(i.__ieee754_rem_pio2) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    rred.o(i.__ieee754_rem_pio2) refers to daddsub_clz.o(x$fpl$dsub) for __aeabi_dsub
    rred.o(i.__ieee754_rem_pio2) refers to daddsub_clz.o(x$fpl$dadd) for __aeabi_dadd
    rred.o(i.__ieee754_rem_pio2) refers to fabs.o(i.fabs) for fabs
    rred.o(i.__ieee754_rem_pio2) refers to dmul.o(x$fpl$dmul) for __aeabi_dmul
    rred.o(i.__ieee754_rem_pio2) refers to dfix.o(x$fpl$dfix) for __aeabi_d2iz
    rred.o(i.__ieee754_rem_pio2) refers to dflt_clz.o(x$fpl$dflt) for __aeabi_i2d
    rred.o(i.__ieee754_rem_pio2) refers to daddsub_clz.o(x$fpl$drsb) for __aeabi_drsub
    rred.o(i.__ieee754_rem_pio2) refers to basic.o(x$fpl$basic) for __aeabi_dneg
    rred.o(i.__ieee754_rem_pio2) refers to dflt_clz.o(x$fpl$dfltu) for __aeabi_ui2d
    rred.o(i.__ieee754_rem_pio2) refers to rred.o(.constdata) for .constdata
    rred.o(i.__use_accurate_range_reduction) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    rred.o(.constdata) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    tan_i.o(i.__kernel_tan) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    tan_i.o(i.__kernel_tan) refers to dfix.o(x$fpl$dfix) for __aeabi_d2iz
    tan_i.o(i.__kernel_tan) refers to basic.o(x$fpl$basic) for __aeabi_dneg
    tan_i.o(i.__kernel_tan) refers to daddsub_clz.o(x$fpl$drsb) for __aeabi_drsub
    tan_i.o(i.__kernel_tan) refers to daddsub_clz.o(x$fpl$dsub) for __aeabi_dsub
    tan_i.o(i.__kernel_tan) refers to daddsub_clz.o(x$fpl$dadd) for __aeabi_dadd
    tan_i.o(i.__kernel_tan) refers to dmul.o(x$fpl$dmul) for __aeabi_dmul
    tan_i.o(i.__kernel_tan) refers to poly.o(i.__kernel_poly) for __kernel_poly
    tan_i.o(i.__kernel_tan) refers to dflt_clz.o(x$fpl$dflt) for __aeabi_i2d
    tan_i.o(i.__kernel_tan) refers to ddiv.o(x$fpl$ddiv) for __aeabi_ddiv
    tan_i.o(i.__kernel_tan) refers to fabs.o(i.fabs) for fabs
    tan_i.o(i.__kernel_tan) refers to fpclassify.o(i.__ARM_fpclassify) for __ARM_fpclassify
    tan_i.o(i.__kernel_tan) refers to dunder.o(i.__mathlib_dbl_underflow) for __mathlib_dbl_underflow
    tan_i.o(i.__kernel_tan) refers to tan_i.o(.constdata) for .constdata
    tan_i.o(.constdata) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    tan_i_x.o(i.____kernel_tan$lsc) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    tan_i_x.o(i.____kernel_tan$lsc) refers to dfix.o(x$fpl$dfix) for __aeabi_d2iz
    tan_i_x.o(i.____kernel_tan$lsc) refers to basic.o(x$fpl$basic) for __aeabi_dneg
    tan_i_x.o(i.____kernel_tan$lsc) refers to daddsub_clz.o(x$fpl$drsb) for __aeabi_drsub
    tan_i_x.o(i.____kernel_tan$lsc) refers to daddsub_clz.o(x$fpl$dsub) for __aeabi_dsub
    tan_i_x.o(i.____kernel_tan$lsc) refers to daddsub_clz.o(x$fpl$dadd) for __aeabi_dadd
    tan_i_x.o(i.____kernel_tan$lsc) refers to dmul.o(x$fpl$dmul) for __aeabi_dmul
    tan_i_x.o(i.____kernel_tan$lsc) refers to poly.o(i.__kernel_poly) for __kernel_poly
    tan_i_x.o(i.____kernel_tan$lsc) refers to dflt_clz.o(x$fpl$dflt) for __aeabi_i2d
    tan_i_x.o(i.____kernel_tan$lsc) refers to ddiv.o(x$fpl$ddiv) for __aeabi_ddiv
    tan_i_x.o(i.____kernel_tan$lsc) refers to fabs.o(i.fabs) for fabs
    tan_i_x.o(i.____kernel_tan$lsc) refers to tan_i_x.o(.constdata) for .constdata
    tan_i_x.o(.constdata) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    __rtentry2.o(.ARM.Collect$$rtentry$$00000008) refers to boardinit2.o(.text) for _platform_post_stackheap_init
    __rtentry2.o(.ARM.Collect$$rtentry$$0000000A) refers to libinit.o(.ARM.Collect$$libinit$$00000000) for __rt_lib_init
    __rtentry2.o(.ARM.Collect$$rtentry$$0000000B) refers to boardinit3.o(.text) for _platform_post_lib_init
    __rtentry2.o(.ARM.Collect$$rtentry$$0000000D) refers to main.o(.text) for main
    __rtentry2.o(.ARM.Collect$$rtentry$$0000000D) refers to exit.o(.text) for exit
    __rtentry2.o(.ARM.exidx) refers to __rtentry2.o(.ARM.Collect$$rtentry$$00000001) for .ARM.Collect$$rtentry$$00000001
    __rtentry2.o(.ARM.exidx) refers to __rtentry2.o(.ARM.Collect$$rtentry$$00000008) for .ARM.Collect$$rtentry$$00000008
    __rtentry2.o(.ARM.exidx) refers to __rtentry2.o(.ARM.Collect$$rtentry$$0000000A) for .ARM.Collect$$rtentry$$0000000A
    __rtentry2.o(.ARM.exidx) refers to __rtentry2.o(.ARM.Collect$$rtentry$$0000000B) for .ARM.Collect$$rtentry$$0000000B
    __rtentry2.o(.ARM.exidx) refers to __rtentry2.o(.ARM.Collect$$rtentry$$0000000D) for .ARM.Collect$$rtentry$$0000000D
    __rtentry4.o(.ARM.Collect$$rtentry$$00000004) refers to sys_stackheap_outer.o(.text) for __user_setup_stackheap
    __rtentry4.o(.ARM.exidx) refers to __rtentry4.o(.ARM.Collect$$rtentry$$00000004) for .ARM.Collect$$rtentry$$00000004
    rt_locale.o(.text) refers to rt_locale.o(.bss) for __rt_locale_data
    rt_locale_intlibspace.o(.text) refers to libspace.o(.bss) for __libspace_start
    rt_errno_addr.o(.text) refers to rt_errno_addr.o(.bss) for __aeabi_errno_addr_data
    rt_errno_addr_intlibspace.o(.text) refers to libspace.o(.bss) for __libspace_start
    bigflt0.o(.text) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    bigflt0.o(.text) refers to btod.o(CL$$btod_emul) for _btod_emul
    bigflt0.o(.text) refers to btod.o(CL$$btod_ediv) for _btod_ediv
    bigflt0.o(.text) refers to bigflt0.o(.constdata) for .constdata
    bigflt0.o(.constdata) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    btod.o(CL$$btod_d2e) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    btod.o(CL$$btod_d2e) refers to btod.o(CL$$btod_d2e_norm_op1) for _d2e_norm_op1
    btod.o(CL$$btod_d2e_norm_op1) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    btod.o(CL$$btod_d2e_norm_op1) refers to btod.o(CL$$btod_d2e_denorm_low) for _d2e_denorm_low
    btod.o(CL$$btod_d2e_denorm_low) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    btod.o(CL$$btod_emul) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    btod.o(CL$$btod_emul) refers to btod.o(CL$$btod_mult_common) for __btod_mult_common
    btod.o(CL$$btod_emul) refers to btod.o(CL$$btod_e2e) for _e2e
    btod.o(CL$$btod_ediv) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    btod.o(CL$$btod_ediv) refers to btod.o(CL$$btod_div_common) for __btod_div_common
    btod.o(CL$$btod_ediv) refers to btod.o(CL$$btod_e2e) for _e2e
    btod.o(CL$$btod_emuld) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    btod.o(CL$$btod_emuld) refers to btod.o(CL$$btod_mult_common) for __btod_mult_common
    btod.o(CL$$btod_emuld) refers to btod.o(CL$$btod_e2d) for _e2d
    btod.o(CL$$btod_edivd) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    btod.o(CL$$btod_edivd) refers to btod.o(CL$$btod_div_common) for __btod_div_common
    btod.o(CL$$btod_edivd) refers to btod.o(CL$$btod_e2d) for _e2d
    btod.o(CL$$btod_e2e) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    btod.o(CL$$btod_e2d) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    btod.o(CL$$btod_e2d) refers to btod.o(CL$$btod_e2e) for _e2e
    btod.o(CL$$btod_mult_common) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    btod.o(CL$$btod_div_common) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    lc_numeric_c.o(locale$$data) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000016) for __rt_lib_init_lc_numeric_2
    lc_numeric_c.o(locale$$code) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000016) for __rt_lib_init_lc_numeric_2
    lc_numeric_c.o(locale$$code) refers to strcmpv7m.o(.text) for strcmp
    lc_numeric_c.o(locale$$code) refers to lc_numeric_c.o(locale$$data) for __lcnum_c_name
    basic.o(x$fpl$basic) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    istatus.o(x$fpl$ieeestatus) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    fabs.o(i.__hardfp_fabs) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    fabs.o(i.__softfp_fabs) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    fabs.o(i.fabs) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    fpclassify.o(i.__ARM_fpclassify) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    poly.o(i.__kernel_poly) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    poly.o(i.__kernel_poly) refers to dmul.o(x$fpl$dmul) for __aeabi_dmul
    poly.o(i.__kernel_poly) refers to daddsub_clz.o(x$fpl$dadd) for __aeabi_dadd
    libspace.o(.text) refers to libspace.o(.bss) for __libspace_start
    sys_stackheap_outer.o(.text) refers to libspace.o(.text) for __user_perproc_libspace
    sys_stackheap_outer.o(.text) refers to startup_stm32f40_41xxx.o(.text) for __user_initial_stackheap
    exit.o(.text) refers to rtexit.o(.ARM.Collect$$rtexit$$00000000) for __rt_exit
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000002E) for __rt_lib_init_alloca_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000002C) for __rt_lib_init_argv_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000001B) for __rt_lib_init_atexit_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000021) for __rt_lib_init_clock_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000032) for __rt_lib_init_cpp_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000030) for __rt_lib_init_exceptions_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000001) for __rt_lib_init_fp_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000001F) for __rt_lib_init_fp_trap_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000023) for __rt_lib_init_getenv_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000000A) for __rt_lib_init_heap_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000011) for __rt_lib_init_lc_collate_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000013) for __rt_lib_init_lc_ctype_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000015) for __rt_lib_init_lc_monetary_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000017) for __rt_lib_init_lc_numeric_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000019) for __rt_lib_init_lc_time_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000004) for __rt_lib_init_preinit_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000000E) for __rt_lib_init_rand_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000033) for __rt_lib_init_return
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000001D) for __rt_lib_init_signal_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000025) for __rt_lib_init_stdio_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000000C) for __rt_lib_init_user_alloc_1
    libinit2.o(.ARM.Collect$$libinit$$00000001) refers to fpinit.o(x$fpl$fpinit) for _fp_init
    libinit2.o(.ARM.Collect$$libinit$$0000000F) refers (Weak) to rt_locale_intlibspace.o(.text) for __rt_locale
    libinit2.o(.ARM.Collect$$libinit$$00000010) refers to libinit2.o(.ARM.Collect$$libinit$$0000000F) for .ARM.Collect$$libinit$$0000000F
    libinit2.o(.ARM.Collect$$libinit$$00000012) refers to libinit2.o(.ARM.Collect$$libinit$$0000000F) for .ARM.Collect$$libinit$$0000000F
    libinit2.o(.ARM.Collect$$libinit$$00000014) refers to libinit2.o(.ARM.Collect$$libinit$$0000000F) for .ARM.Collect$$libinit$$0000000F
    libinit2.o(.ARM.Collect$$libinit$$00000016) refers to libinit2.o(.ARM.Collect$$libinit$$0000000F) for .ARM.Collect$$libinit$$0000000F
    libinit2.o(.ARM.Collect$$libinit$$00000016) refers (Weak) to lc_numeric_c.o(locale$$code) for _get_lc_numeric
    libinit2.o(.ARM.Collect$$libinit$$00000018) refers to libinit2.o(.ARM.Collect$$libinit$$0000000F) for .ARM.Collect$$libinit$$0000000F
    libinit2.o(.ARM.Collect$$libinit$$00000026) refers to argv_veneer.o(.emb_text) for __ARM_argv_veneer
    libinit2.o(.ARM.Collect$$libinit$$00000027) refers to argv_veneer.o(.emb_text) for __ARM_argv_veneer
    rtexit.o(.ARM.Collect$$rtexit$$00000000) refers (Special) to rtexit2.o(.ARM.Collect$$rtexit$$00000004) for __rt_exit_exit
    rtexit.o(.ARM.Collect$$rtexit$$00000000) refers (Special) to rtexit2.o(.ARM.Collect$$rtexit$$00000003) for __rt_exit_ls
    rtexit.o(.ARM.Collect$$rtexit$$00000000) refers (Special) to rtexit2.o(.ARM.Collect$$rtexit$$00000002) for __rt_exit_prels_1
    rtexit.o(.ARM.exidx) refers (Special) to rtexit2.o(.ARM.Collect$$rtexit$$00000004) for __rt_exit_exit
    rtexit.o(.ARM.exidx) refers (Special) to rtexit2.o(.ARM.Collect$$rtexit$$00000003) for __rt_exit_ls
    rtexit.o(.ARM.exidx) refers (Special) to rtexit2.o(.ARM.Collect$$rtexit$$00000002) for __rt_exit_prels_1
    rtexit.o(.ARM.exidx) refers to rtexit.o(.ARM.Collect$$rtexit$$00000000) for .ARM.Collect$$rtexit$$00000000
    argv_veneer.o(.emb_text) refers to no_argv.o(.text) for __ARM_get_argv
    rtexit2.o(.ARM.Collect$$rtexit$$00000003) refers to libshutdown.o(.ARM.Collect$$libshutdown$$00000000) for __rt_lib_shutdown
    rtexit2.o(.ARM.Collect$$rtexit$$00000004) refers to usart.o(.text) for _sys_exit
    rtexit2.o(.ARM.exidx) refers to rtexit2.o(.ARM.Collect$$rtexit$$00000001) for .ARM.Collect$$rtexit$$00000001
    rtexit2.o(.ARM.exidx) refers to rtexit2.o(.ARM.Collect$$rtexit$$00000003) for .ARM.Collect$$rtexit$$00000003
    rtexit2.o(.ARM.exidx) refers to rtexit2.o(.ARM.Collect$$rtexit$$00000004) for .ARM.Collect$$rtexit$$00000004
    _get_argv_nomalloc.o(.text) refers (Special) to hrguard.o(.text) for __heap_region$guard
    _get_argv_nomalloc.o(.text) refers to defsig_rtmem_outer.o(.text) for __rt_SIGRTMEM
    _get_argv_nomalloc.o(.text) refers to sys_command.o(.text) for _sys_command_string
    sys_command.o(.text) refers (Special) to use_no_semi.o(.text) for __I$use$semihosting
    sys_command.o(.text) refers (Special) to indicate_semi.o(.text) for __semihosting_library_function
    defsig_rtmem_outer.o(.text) refers to defsig_rtmem_inner.o(.text) for __rt_SIGRTMEM_inner
    defsig_rtmem_outer.o(.text) refers to defsig_exit.o(.text) for __sig_exit
    defsig_rtmem_formal.o(.text) refers to rt_raise.o(.text) for __rt_raise
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$00000004) for __rt_lib_shutdown_cpp_1
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$00000002) for __rt_lib_shutdown_fini_1
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$00000009) for __rt_lib_shutdown_fp_trap_1
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$00000011) for __rt_lib_shutdown_heap_1
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$00000012) for __rt_lib_shutdown_return
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$0000000C) for __rt_lib_shutdown_signal_1
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$00000006) for __rt_lib_shutdown_stdio_1
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$0000000E) for __rt_lib_shutdown_user_alloc_1
    rt_raise.o(.text) refers to __raise.o(.text) for __raise
    rt_raise.o(.text) refers to usart.o(.text) for _sys_exit
    defsig_exit.o(.text) refers to usart.o(.text) for _sys_exit
    defsig_rtmem_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    __raise.o(.text) refers to defsig.o(CL$$defsig) for __default_signal_handler
    defsig_general.o(.text) refers to sys_wrch.o(.text) for _ttywrch
    sys_wrch.o(.text) refers (Special) to use_no_semi.o(.text) for __I$use$semihosting
    sys_wrch.o(.text) refers (Special) to indicate_semi.o(.text) for __semihosting_library_function
    defsig.o(CL$$defsig) refers to defsig_rtmem_inner.o(.text) for __rt_SIGRTMEM_inner
    defsig_abrt_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_fpe_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_rtred_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_stak_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_pvfn_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_cppl_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_segv_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_other.o(.text) refers to defsig_general.o(.text) for __default_signal_display


==============================================================================

Removing Unused input sections from the image.

    Removing main.o(.rev16_text), (4 bytes).
    Removing main.o(.revsh_text), (4 bytes).
    Removing main.o(.constdata), (57200 bytes).
    Removing stm32f4xx_it.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_it.o(.revsh_text), (4 bytes).
    Removing system_stm32f4xx.o(.rev16_text), (4 bytes).
    Removing system_stm32f4xx.o(.revsh_text), (4 bytes).
    Removing led.o(.rev16_text), (4 bytes).
    Removing led.o(.revsh_text), (4 bytes).
    Removing key.o(.rev16_text), (4 bytes).
    Removing key.o(.revsh_text), (4 bytes).
    Removing timer.o(.rev16_text), (4 bytes).
    Removing timer.o(.revsh_text), (4 bytes).
    Removing adc_dma_timer.o(.rev16_text), (4 bytes).
    Removing adc_dma_timer.o(.revsh_text), (4 bytes).
    Removing lcd.o(.rev16_text), (4 bytes).
    Removing lcd.o(.revsh_text), (4 bytes).
    Removing dac.o(.rev16_text), (4 bytes).
    Removing dac.o(.revsh_text), (4 bytes).
    Removing dac.o(.text), (212 bytes).
    Removing ad9833.o(.rev16_text), (4 bytes).
    Removing ad9833.o(.revsh_text), (4 bytes).
    Removing mcp41010.o(.rev16_text), (4 bytes).
    Removing mcp41010.o(.revsh_text), (4 bytes).
    Removing ctiic.o(.rev16_text), (4 bytes).
    Removing ctiic.o(.revsh_text), (4 bytes).
    Removing ft5206.o(.rev16_text), (4 bytes).
    Removing ft5206.o(.revsh_text), (4 bytes).
    Removing gt9147.o(.rev16_text), (4 bytes).
    Removing gt9147.o(.revsh_text), (4 bytes).
    Removing ott2001a.o(.rev16_text), (4 bytes).
    Removing ott2001a.o(.revsh_text), (4 bytes).
    Removing touch.o(.rev16_text), (4 bytes).
    Removing touch.o(.revsh_text), (4 bytes).
    Removing 24cxx.o(.rev16_text), (4 bytes).
    Removing 24cxx.o(.revsh_text), (4 bytes).
    Removing myiic.o(.rev16_text), (4 bytes).
    Removing myiic.o(.revsh_text), (4 bytes).
    Removing ui.o(.rev16_text), (4 bytes).
    Removing ui.o(.revsh_text), (4 bytes).
    Removing control.o(.rev16_text), (4 bytes).
    Removing control.o(.revsh_text), (4 bytes).
    Removing filter.o(.rev16_text), (4 bytes).
    Removing filter.o(.revsh_text), (4 bytes).
    Removing filter.o(.text), (552 bytes).
    Removing dsp_process.o(.rev16_text), (4 bytes).
    Removing dsp_process.o(.revsh_text), (4 bytes).
    Removing delay.o(.rev16_text), (4 bytes).
    Removing delay.o(.revsh_text), (4 bytes).
    Removing sys.o(.rev16_text), (4 bytes).
    Removing sys.o(.revsh_text), (4 bytes).
    Removing sys.o(.emb_text), (16 bytes).
    Removing usart.o(.rev16_text), (4 bytes).
    Removing usart.o(.revsh_text), (4 bytes).
    Removing misc.o(.rev16_text), (4 bytes).
    Removing misc.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_gpio.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_gpio.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_fsmc.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_fsmc.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_rcc.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_rcc.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_syscfg.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_syscfg.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_syscfg.o(.text), (148 bytes).
    Removing stm32f4xx_usart.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_usart.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_tim.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_tim.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_adc.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_adc.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_dma.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_dma.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_dac.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_dac.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_dac.o(.text), (528 bytes).
    Removing arm_cfft_radix4_f32.o(.rev16_text), (4 bytes).
    Removing arm_cfft_radix4_f32.o(.revsh_text), (4 bytes).
    Removing arm_cfft_radix4_init_f32.o(.rev16_text), (4 bytes).
    Removing arm_cfft_radix4_init_f32.o(.revsh_text), (4 bytes).
    Removing arm_bitreversal.o(.rev16_text), (4 bytes).
    Removing arm_bitreversal.o(.revsh_text), (4 bytes).
    Removing arm_common_tables.o(.rev16_text), (4 bytes).
    Removing arm_common_tables.o(.revsh_text), (4 bytes).
    Removing arm_common_tables.o(.constdata), (128 bytes).
    Removing arm_common_tables.o(.constdata), (256 bytes).
    Removing arm_common_tables.o(.constdata), (512 bytes).
    Removing arm_common_tables.o(.constdata), (1024 bytes).
    Removing arm_common_tables.o(.constdata), (2048 bytes).
    Removing arm_common_tables.o(.constdata), (4096 bytes).
    Removing arm_common_tables.o(.constdata), (8192 bytes).
    Removing arm_common_tables.o(.constdata), (16384 bytes).
    Removing arm_common_tables.o(.constdata), (24576 bytes).
    Removing arm_common_tables.o(.constdata), (12288 bytes).
    Removing arm_common_tables.o(.constdata), (128 bytes).
    Removing arm_common_tables.o(.constdata), (256 bytes).
    Removing arm_common_tables.o(.constdata), (40 bytes).
    Removing arm_common_tables.o(.constdata), (96 bytes).
    Removing arm_common_tables.o(.constdata), (112 bytes).
    Removing arm_common_tables.o(.constdata), (416 bytes).
    Removing arm_common_tables.o(.constdata), (880 bytes).
    Removing arm_common_tables.o(.constdata), (896 bytes).
    Removing arm_common_tables.o(.constdata), (3600 bytes).
    Removing arm_common_tables.o(.constdata), (7616 bytes).
    Removing arm_common_tables.o(.constdata), (8064 bytes).
    Removing arm_common_tables.o(.constdata), (128 bytes).
    Removing arm_common_tables.o(.constdata), (256 bytes).
    Removing arm_common_tables.o(.constdata), (512 bytes).
    Removing arm_common_tables.o(.constdata), (1024 bytes).
    Removing arm_common_tables.o(.constdata), (2048 bytes).
    Removing arm_common_tables.o(.constdata), (4096 bytes).
    Removing arm_common_tables.o(.constdata), (8192 bytes).
    Removing arm_common_tables.o(.constdata), (16384 bytes).

113 unused section(s) (total 183216 bytes) removed from the image.

==============================================================================

Image Symbol Table

    Local Symbols

    Symbol Name                              Value     Ov Type        Size  Object(Section)

    ../clib/angel/boardlib.s                 0x00000000   Number         0  boardinit3.o ABSOLUTE
    ../clib/angel/boardlib.s                 0x00000000   Number         0  boardinit1.o ABSOLUTE
    ../clib/angel/boardlib.s                 0x00000000   Number         0  boardshut.o ABSOLUTE
    ../clib/angel/boardlib.s                 0x00000000   Number         0  boardinit2.o ABSOLUTE
    ../clib/angel/handlers.s                 0x00000000   Number         0  __scatter_copy.o ABSOLUTE
    ../clib/angel/handlers.s                 0x00000000   Number         0  __scatter_zi.o ABSOLUTE
    ../clib/angel/kernel.s                   0x00000000   Number         0  __rtentry2.o ABSOLUTE
    ../clib/angel/kernel.s                   0x00000000   Number         0  __rtentry.o ABSOLUTE
    ../clib/angel/kernel.s                   0x00000000   Number         0  rtexit2.o ABSOLUTE
    ../clib/angel/kernel.s                   0x00000000   Number         0  rtexit.o ABSOLUTE
    ../clib/angel/kernel.s                   0x00000000   Number         0  __rtentry4.o ABSOLUTE
    ../clib/angel/rt.s                       0x00000000   Number         0  rt_locale.o ABSOLUTE
    ../clib/angel/rt.s                       0x00000000   Number         0  rt_errno_addr_intlibspace.o ABSOLUTE
    ../clib/angel/rt.s                       0x00000000   Number         0  rt_errno_addr.o ABSOLUTE
    ../clib/angel/rt.s                       0x00000000   Number         0  rt_raise.o ABSOLUTE
    ../clib/angel/rt.s                       0x00000000   Number         0  rt_locale_intlibspace.o ABSOLUTE
    ../clib/angel/scatter.s                  0x00000000   Number         0  __scatter.o ABSOLUTE
    ../clib/angel/startup.s                  0x00000000   Number         0  __main.o ABSOLUTE
    ../clib/angel/sys.s                      0x00000000   Number         0  libspace.o ABSOLUTE
    ../clib/angel/sys.s                      0x00000000   Number         0  sys_stackheap_outer.o ABSOLUTE
    ../clib/angel/sys.s                      0x00000000   Number         0  use_no_semi_2.o ABSOLUTE
    ../clib/angel/sys.s                      0x00000000   Number         0  use_no_semi.o ABSOLUTE
    ../clib/angel/sys.s                      0x00000000   Number         0  indicate_semi.o ABSOLUTE
    ../clib/angel/sysapp.c                   0x00000000   Number         0  sys_wrch.o ABSOLUTE
    ../clib/angel/sysapp.c                   0x00000000   Number         0  sys_command.o ABSOLUTE
    ../clib/armsys.c                         0x00000000   Number         0  _get_argv_nomalloc.o ABSOLUTE
    ../clib/armsys.c                         0x00000000   Number         0  argv_veneer.o ABSOLUTE
    ../clib/armsys.c                         0x00000000   Number         0  no_argv.o ABSOLUTE
    ../clib/armsys.c                         0x00000000   Number         0  argv_veneer.o ABSOLUTE
    ../clib/bigflt.c                         0x00000000   Number         0  bigflt0.o ABSOLUTE
    ../clib/btod.s                           0x00000000   Number         0  btod.o ABSOLUTE
    ../clib/fenv.c                           0x00000000   Number         0  _rserrno.o ABSOLUTE
    ../clib/heapalloc.c                      0x00000000   Number         0  hrguard.o ABSOLUTE
    ../clib/heapaux.c                        0x00000000   Number         0  heapauxi.o ABSOLUTE
    ../clib/libinit.s                        0x00000000   Number         0  libinit2.o ABSOLUTE
    ../clib/libinit.s                        0x00000000   Number         0  libshutdown2.o ABSOLUTE
    ../clib/libinit.s                        0x00000000   Number         0  libshutdown.o ABSOLUTE
    ../clib/libinit.s                        0x00000000   Number         0  libinit.o ABSOLUTE
    ../clib/locale.s                         0x00000000   Number         0  lc_numeric_c.o ABSOLUTE
    ../clib/longlong.s                       0x00000000   Number         0  lludiv10.o ABSOLUTE
    ../clib/memcpset.s                       0x00000000   Number         0  rt_memclr_w.o ABSOLUTE
    ../clib/memcpset.s                       0x00000000   Number         0  rt_memclr.o ABSOLUTE
    ../clib/memcpset.s                       0x00000000   Number         0  strcmpv7m.o ABSOLUTE
    ../clib/misc.s                           0x00000000   Number         0  printf_stubs.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_hex_int.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  noretval__2sprintf.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __printf_wp.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  noretval__2printf.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __2printf.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_hex_int_ll.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_hex_ptr.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_hex_int_ptr.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_hex_ll_ptr.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_hex_int_ll_ptr.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __2sprintf.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_intcommon.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_fp_dec.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_char_common.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _sputc.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_char.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_char_file.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __printf_flags.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __printf_ss.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_str.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __printf_flags_ss.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __printf_nopercent.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __printf_flags_ss_wp.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __printf_ss_wp.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __printf_flags_wp.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_fp_infnan.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_dec.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __printf.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_hex_ll.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_s.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_d.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_percent.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_x.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_f.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_percent_end.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_general.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_rtmem_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_exit.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_abrt_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_fpe_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_rtred_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_stak_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  __raise.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_rtmem_formal.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_rtmem_outer.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_pvfn_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_cppl_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_segv_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_other.o ABSOLUTE
    ../clib/signal.s                         0x00000000   Number         0  defsig.o ABSOLUTE
    ../clib/stdio.c                          0x00000000   Number         0  ferror.o ABSOLUTE
    ../clib/stdio.c                          0x00000000   Number         0  ferror_locked.o ABSOLUTE
    ../clib/stdlib.c                         0x00000000   Number         0  exit.o ABSOLUTE
    ../clib/string.c                         0x00000000   Number         0  strcat.o ABSOLUTE
    ../fplib/basic.s                         0x00000000   Number         0  basic.o ABSOLUTE
    ../fplib/d2f.s                           0x00000000   Number         0  d2f.o ABSOLUTE
    ../fplib/daddsub.s                       0x00000000   Number         0  daddsub_clz.o ABSOLUTE
    ../fplib/dcmpi.s                         0x00000000   Number         0  dcmpi.o ABSOLUTE
    ../fplib/ddiv.s                          0x00000000   Number         0  ddiv.o ABSOLUTE
    ../fplib/dfix.s                          0x00000000   Number         0  dfix.o ABSOLUTE
    ../fplib/dfixu.s                         0x00000000   Number         0  dfixu.o ABSOLUTE
    ../fplib/dflt.s                          0x00000000   Number         0  dflt_clz.o ABSOLUTE
    ../fplib/dleqf.s                         0x00000000   Number         0  dleqf.o ABSOLUTE
    ../fplib/dmul.s                          0x00000000   Number         0  dmul.o ABSOLUTE
    ../fplib/dnaninf.s                       0x00000000   Number         0  dnaninf.o ABSOLUTE
    ../fplib/dretinf.s                       0x00000000   Number         0  dretinf.o ABSOLUTE
    ../fplib/drleqf.s                        0x00000000   Number         0  drleqf.o ABSOLUTE
    ../fplib/dsqrt.s                         0x00000000   Number         0  dsqrt_umaal.o ABSOLUTE
    ../fplib/f2d.s                           0x00000000   Number         0  f2d.o ABSOLUTE
    ../fplib/fnaninf.s                       0x00000000   Number         0  fnaninf.o ABSOLUTE
    ../fplib/fpinit.s                        0x00000000   Number         0  fpinit.o ABSOLUTE
    ../fplib/fretinf.s                       0x00000000   Number         0  fretinf.o ABSOLUTE
    ../fplib/istatus.s                       0x00000000   Number         0  istatus.o ABSOLUTE
    ../fplib/printf1.s                       0x00000000   Number         0  printf1.o ABSOLUTE
    ../fplib/usenofp.s                       0x00000000   Number         0  usenofp.o ABSOLUTE
    ../mathlib/dunder.c                      0x00000000   Number         0  dunder.o ABSOLUTE
    ../mathlib/fabs.c                        0x00000000   Number         0  fabs.o ABSOLUTE
    ../mathlib/fpclassify.c                  0x00000000   Number         0  fpclassify.o ABSOLUTE
    ../mathlib/poly.c                        0x00000000   Number         0  poly.o ABSOLUTE
    ../mathlib/rred.c                        0x00000000   Number         0  rred.o ABSOLUTE
    ../mathlib/sqrt.c                        0x00000000   Number         0  sqrt_x.o ABSOLUTE
    ../mathlib/sqrt.c                        0x00000000   Number         0  sqrt.o ABSOLUTE
    ../mathlib/tan.c                         0x00000000   Number         0  tan_x.o ABSOLUTE
    ../mathlib/tan.c                         0x00000000   Number         0  tan.o ABSOLUTE
    ../mathlib/tan_i.c                       0x00000000   Number         0  tan_i_x.o ABSOLUTE
    ../mathlib/tan_i.c                       0x00000000   Number         0  tan_i.o ABSOLUTE
    ..\CORE\startup_stm32f40_41xxx.s         0x00000000   Number         0  startup_stm32f40_41xxx.o ABSOLUTE
    ..\CommonTables\arm_common_tables.c      0x00000000   Number         0  arm_common_tables.o ABSOLUTE
    ..\FWLIB\src\misc.c                      0x00000000   Number         0  misc.o ABSOLUTE
    ..\FWLIB\src\stm32f4xx_adc.c             0x00000000   Number         0  stm32f4xx_adc.o ABSOLUTE
    ..\FWLIB\src\stm32f4xx_dac.c             0x00000000   Number         0  stm32f4xx_dac.o ABSOLUTE
    ..\FWLIB\src\stm32f4xx_dma.c             0x00000000   Number         0  stm32f4xx_dma.o ABSOLUTE
    ..\FWLIB\src\stm32f4xx_fsmc.c            0x00000000   Number         0  stm32f4xx_fsmc.o ABSOLUTE
    ..\FWLIB\src\stm32f4xx_gpio.c            0x00000000   Number         0  stm32f4xx_gpio.o ABSOLUTE
    ..\FWLIB\src\stm32f4xx_rcc.c             0x00000000   Number         0  stm32f4xx_rcc.o ABSOLUTE
    ..\FWLIB\src\stm32f4xx_syscfg.c          0x00000000   Number         0  stm32f4xx_syscfg.o ABSOLUTE
    ..\FWLIB\src\stm32f4xx_tim.c             0x00000000   Number         0  stm32f4xx_tim.o ABSOLUTE
    ..\FWLIB\src\stm32f4xx_usart.c           0x00000000   Number         0  stm32f4xx_usart.o ABSOLUTE
    ..\HARDWARE\24CXX\24cxx.c                0x00000000   Number         0  24cxx.o ABSOLUTE
    ..\HARDWARE\AD9833\ad9833.c              0x00000000   Number         0  ad9833.o ABSOLUTE
    ..\HARDWARE\ADC\adc_dma_timer.c          0x00000000   Number         0  adc_dma_timer.o ABSOLUTE
    ..\HARDWARE\CONTROL\control.c            0x00000000   Number         0  control.o ABSOLUTE
    ..\HARDWARE\DAC\dac.c                    0x00000000   Number         0  dac.o ABSOLUTE
    ..\HARDWARE\FILTER\filter.c              0x00000000   Number         0  filter.o ABSOLUTE
    ..\HARDWARE\IIC\myiic.c                  0x00000000   Number         0  myiic.o ABSOLUTE
    ..\HARDWARE\KEY\key.c                    0x00000000   Number         0  key.o ABSOLUTE
    ..\HARDWARE\LCD\lcd.c                    0x00000000   Number         0  lcd.o ABSOLUTE
    ..\HARDWARE\LED\led.c                    0x00000000   Number         0  led.o ABSOLUTE
    ..\HARDWARE\MCP41010\MCP41010.c          0x00000000   Number         0  mcp41010.o ABSOLUTE
    ..\HARDWARE\SEARCH\search.c              0x00000000   Number         0  search.o ABSOLUTE
    ..\HARDWARE\TIMER\timer.c                0x00000000   Number         0  timer.o ABSOLUTE
    ..\HARDWARE\TOUCH\ctiic.c                0x00000000   Number         0  ctiic.o ABSOLUTE
    ..\HARDWARE\TOUCH\ft5206.c               0x00000000   Number         0  ft5206.o ABSOLUTE
    ..\HARDWARE\TOUCH\gt9147.c               0x00000000   Number         0  gt9147.o ABSOLUTE
    ..\HARDWARE\TOUCH\ott2001a.c             0x00000000   Number         0  ott2001a.o ABSOLUTE
    ..\HARDWARE\TOUCH\touch.c                0x00000000   Number         0  touch.o ABSOLUTE
    ..\HARDWARE\UI\ui.c                      0x00000000   Number         0  ui.o ABSOLUTE
    ..\HARDWARE\dsp_process\dsp_process.c    0x00000000   Number         0  dsp_process.o ABSOLUTE
    ..\SYSTEM\delay\delay.c                  0x00000000   Number         0  delay.o ABSOLUTE
    ..\SYSTEM\sys\sys.c                      0x00000000   Number         0  sys.o ABSOLUTE
    ..\SYSTEM\usart\usart.c                  0x00000000   Number         0  usart.o ABSOLUTE
    ..\TransformFunctions\arm_bitreversal.c  0x00000000   Number         0  arm_bitreversal.o ABSOLUTE
    ..\TransformFunctions\arm_cfft_radix4_f32.c 0x00000000   Number         0  arm_cfft_radix4_f32.o ABSOLUTE
    ..\TransformFunctions\arm_cfft_radix4_init_f32.c 0x00000000   Number         0  arm_cfft_radix4_init_f32.o ABSOLUTE
    ..\\CommonTables\\arm_common_tables.c    0x00000000   Number         0  arm_common_tables.o ABSOLUTE
    ..\\FWLIB\\src\\misc.c                   0x00000000   Number         0  misc.o ABSOLUTE
    ..\\FWLIB\\src\\stm32f4xx_adc.c          0x00000000   Number         0  stm32f4xx_adc.o ABSOLUTE
    ..\\FWLIB\\src\\stm32f4xx_dac.c          0x00000000   Number         0  stm32f4xx_dac.o ABSOLUTE
    ..\\FWLIB\\src\\stm32f4xx_dma.c          0x00000000   Number         0  stm32f4xx_dma.o ABSOLUTE
    ..\\FWLIB\\src\\stm32f4xx_fsmc.c         0x00000000   Number         0  stm32f4xx_fsmc.o ABSOLUTE
    ..\\FWLIB\\src\\stm32f4xx_gpio.c         0x00000000   Number         0  stm32f4xx_gpio.o ABSOLUTE
    ..\\FWLIB\\src\\stm32f4xx_rcc.c          0x00000000   Number         0  stm32f4xx_rcc.o ABSOLUTE
    ..\\FWLIB\\src\\stm32f4xx_syscfg.c       0x00000000   Number         0  stm32f4xx_syscfg.o ABSOLUTE
    ..\\FWLIB\\src\\stm32f4xx_tim.c          0x00000000   Number         0  stm32f4xx_tim.o ABSOLUTE
    ..\\FWLIB\\src\\stm32f4xx_usart.c        0x00000000   Number         0  stm32f4xx_usart.o ABSOLUTE
    ..\\HARDWARE\\24CXX\\24cxx.c             0x00000000   Number         0  24cxx.o ABSOLUTE
    ..\\HARDWARE\\AD9833\\ad9833.c           0x00000000   Number         0  ad9833.o ABSOLUTE
    ..\\HARDWARE\\ADC\\adc_dma_timer.c       0x00000000   Number         0  adc_dma_timer.o ABSOLUTE
    ..\\HARDWARE\\CONTROL\\control.c         0x00000000   Number         0  control.o ABSOLUTE
    ..\\HARDWARE\\DAC\\dac.c                 0x00000000   Number         0  dac.o ABSOLUTE
    ..\\HARDWARE\\FILTER\\filter.c           0x00000000   Number         0  filter.o ABSOLUTE
    ..\\HARDWARE\\IIC\\myiic.c               0x00000000   Number         0  myiic.o ABSOLUTE
    ..\\HARDWARE\\KEY\\key.c                 0x00000000   Number         0  key.o ABSOLUTE
    ..\\HARDWARE\\LCD\\lcd.c                 0x00000000   Number         0  lcd.o ABSOLUTE
    ..\\HARDWARE\\LED\\led.c                 0x00000000   Number         0  led.o ABSOLUTE
    ..\\HARDWARE\\MCP41010\\MCP41010.c       0x00000000   Number         0  mcp41010.o ABSOLUTE
    ..\\HARDWARE\\TIMER\\timer.c             0x00000000   Number         0  timer.o ABSOLUTE
    ..\\HARDWARE\\TOUCH\\ctiic.c             0x00000000   Number         0  ctiic.o ABSOLUTE
    ..\\HARDWARE\\TOUCH\\ft5206.c            0x00000000   Number         0  ft5206.o ABSOLUTE
    ..\\HARDWARE\\TOUCH\\gt9147.c            0x00000000   Number         0  gt9147.o ABSOLUTE
    ..\\HARDWARE\\TOUCH\\ott2001a.c          0x00000000   Number         0  ott2001a.o ABSOLUTE
    ..\\HARDWARE\\TOUCH\\touch.c             0x00000000   Number         0  touch.o ABSOLUTE
    ..\\HARDWARE\\UI\\ui.c                   0x00000000   Number         0  ui.o ABSOLUTE
    ..\\HARDWARE\\dsp_process\\dsp_process.c 0x00000000   Number         0  dsp_process.o ABSOLUTE
    ..\\SYSTEM\\delay\\delay.c               0x00000000   Number         0  delay.o ABSOLUTE
    ..\\SYSTEM\\sys\\sys.c                   0x00000000   Number         0  sys.o ABSOLUTE
    ..\\SYSTEM\\usart\\usart.c               0x00000000   Number         0  usart.o ABSOLUTE
    ..\\TransformFunctions\\arm_bitreversal.c 0x00000000   Number         0  arm_bitreversal.o ABSOLUTE
    ..\\TransformFunctions\\arm_cfft_radix4_f32.c 0x00000000   Number         0  arm_cfft_radix4_f32.o ABSOLUTE
    ..\\TransformFunctions\\arm_cfft_radix4_init_f32.c 0x00000000   Number         0  arm_cfft_radix4_init_f32.o ABSOLUTE
    dc.s                                     0x00000000   Number         0  dc.o ABSOLUTE
    main.c                                   0x00000000   Number         0  main.o ABSOLUTE
    main.c                                   0x00000000   Number         0  main.o ABSOLUTE
    stm32f4xx_it.c                           0x00000000   Number         0  stm32f4xx_it.o ABSOLUTE
    stm32f4xx_it.c                           0x00000000   Number         0  stm32f4xx_it.o ABSOLUTE
    system_stm32f4xx.c                       0x00000000   Number         0  system_stm32f4xx.o ABSOLUTE
    system_stm32f4xx.c                       0x00000000   Number         0  system_stm32f4xx.o ABSOLUTE
    RESET                                    0x08000000   Section      392  startup_stm32f40_41xxx.o(RESET)
    !!!main                                  0x08000188   Section        8  __main.o(!!!main)
    !!!scatter                               0x08000190   Section       52  __scatter.o(!!!scatter)
    !!handler_copy                           0x080001c4   Section       26  __scatter_copy.o(!!handler_copy)
    !!handler_zi                             0x080001e0   Section       28  __scatter_zi.o(!!handler_zi)
    .ARM.Collect$$_printf_percent$$00000000  0x080001fc   Section        0  _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000)
    .ARM.Collect$$_printf_percent$$00000003  0x080001fc   Section        6  _printf_f.o(.ARM.Collect$$_printf_percent$$00000003)
    .ARM.Collect$$_printf_percent$$00000009  0x08000202   Section        6  _printf_d.o(.ARM.Collect$$_printf_percent$$00000009)
    .ARM.Collect$$_printf_percent$$0000000C  0x08000208   Section        6  _printf_x.o(.ARM.Collect$$_printf_percent$$0000000C)
    .ARM.Collect$$_printf_percent$$00000014  0x0800020e   Section        6  _printf_s.o(.ARM.Collect$$_printf_percent$$00000014)
    .ARM.Collect$$_printf_percent$$00000017  0x08000214   Section        4  _printf_percent_end.o(.ARM.Collect$$_printf_percent$$00000017)
    .ARM.Collect$$libinit$$00000000          0x08000218   Section        2  libinit.o(.ARM.Collect$$libinit$$00000000)
    .ARM.Collect$$libinit$$00000001          0x0800021a   Section        4  libinit2.o(.ARM.Collect$$libinit$$00000001)
    .ARM.Collect$$libinit$$00000004          0x0800021e   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000004)
    .ARM.Collect$$libinit$$0000000A          0x0800021e   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000000A)
    .ARM.Collect$$libinit$$0000000C          0x0800021e   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000000C)
    .ARM.Collect$$libinit$$0000000E          0x0800021e   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000000E)
    .ARM.Collect$$libinit$$0000000F          0x0800021e   Section        6  libinit2.o(.ARM.Collect$$libinit$$0000000F)
    .ARM.Collect$$libinit$$00000011          0x08000224   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000011)
    .ARM.Collect$$libinit$$00000013          0x08000224   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000013)
    .ARM.Collect$$libinit$$00000015          0x08000224   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000015)
    .ARM.Collect$$libinit$$00000016          0x08000224   Section       10  libinit2.o(.ARM.Collect$$libinit$$00000016)
    .ARM.Collect$$libinit$$00000017          0x0800022e   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000017)
    .ARM.Collect$$libinit$$00000019          0x0800022e   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000019)
    .ARM.Collect$$libinit$$0000001B          0x0800022e   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000001B)
    .ARM.Collect$$libinit$$0000001D          0x0800022e   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000001D)
    .ARM.Collect$$libinit$$0000001F          0x0800022e   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000001F)
    .ARM.Collect$$libinit$$00000021          0x0800022e   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000021)
    .ARM.Collect$$libinit$$00000023          0x0800022e   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000023)
    .ARM.Collect$$libinit$$00000025          0x0800022e   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000025)
    .ARM.Collect$$libinit$$0000002C          0x0800022e   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000002C)
    .ARM.Collect$$libinit$$0000002E          0x0800022e   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000002E)
    .ARM.Collect$$libinit$$00000030          0x0800022e   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000030)
    .ARM.Collect$$libinit$$00000032          0x0800022e   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000032)
    .ARM.Collect$$libinit$$00000033          0x0800022e   Section        2  libinit2.o(.ARM.Collect$$libinit$$00000033)
    .ARM.Collect$$libshutdown$$00000000      0x08000230   Section        2  libshutdown.o(.ARM.Collect$$libshutdown$$00000000)
    .ARM.Collect$$libshutdown$$00000002      0x08000232   Section        0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000002)
    .ARM.Collect$$libshutdown$$00000004      0x08000232   Section        0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000004)
    .ARM.Collect$$libshutdown$$00000006      0x08000232   Section        0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000006)
    .ARM.Collect$$libshutdown$$00000009      0x08000232   Section        0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000009)
    .ARM.Collect$$libshutdown$$0000000C      0x08000232   Section        0  libshutdown2.o(.ARM.Collect$$libshutdown$$0000000C)
    .ARM.Collect$$libshutdown$$0000000E      0x08000232   Section        0  libshutdown2.o(.ARM.Collect$$libshutdown$$0000000E)
    .ARM.Collect$$libshutdown$$00000011      0x08000232   Section        0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000011)
    .ARM.Collect$$libshutdown$$00000012      0x08000232   Section        2  libshutdown2.o(.ARM.Collect$$libshutdown$$00000012)
    .ARM.Collect$$rtentry$$00000000          0x08000234   Section        0  __rtentry.o(.ARM.Collect$$rtentry$$00000000)
    .ARM.Collect$$rtentry$$00000002          0x08000234   Section        0  __rtentry2.o(.ARM.Collect$$rtentry$$00000002)
    .ARM.Collect$$rtentry$$00000004          0x08000234   Section        6  __rtentry4.o(.ARM.Collect$$rtentry$$00000004)
    .ARM.Collect$$rtentry$$00000009          0x0800023a   Section        0  __rtentry2.o(.ARM.Collect$$rtentry$$00000009)
    .ARM.Collect$$rtentry$$0000000A          0x0800023a   Section        4  __rtentry2.o(.ARM.Collect$$rtentry$$0000000A)
    .ARM.Collect$$rtentry$$0000000C          0x0800023e   Section        0  __rtentry2.o(.ARM.Collect$$rtentry$$0000000C)
    .ARM.Collect$$rtentry$$0000000D          0x0800023e   Section        8  __rtentry2.o(.ARM.Collect$$rtentry$$0000000D)
    .ARM.Collect$$rtexit$$00000000           0x08000246   Section        2  rtexit.o(.ARM.Collect$$rtexit$$00000000)
    .ARM.Collect$$rtexit$$00000002           0x08000248   Section        0  rtexit2.o(.ARM.Collect$$rtexit$$00000002)
    .ARM.Collect$$rtexit$$00000003           0x08000248   Section        4  rtexit2.o(.ARM.Collect$$rtexit$$00000003)
    .ARM.Collect$$rtexit$$00000004           0x0800024c   Section        6  rtexit2.o(.ARM.Collect$$rtexit$$00000004)
    .text                                    0x08000254   Section        0  main.o(.text)
    .text                                    0x0800032c   Section        0  stm32f4xx_it.o(.text)
    .text                                    0x08000488   Section        0  system_stm32f4xx.o(.text)
    SetSysClock                              0x08000489   Thumb Code   220  system_stm32f4xx.o(.text)
    .text                                    0x08000698   Section        0  led.o(.text)
    .text                                    0x080006d8   Section        0  key.o(.text)
    .text                                    0x08000794   Section        0  timer.o(.text)
    .text                                    0x08000804   Section        0  adc_dma_timer.o(.text)
    .text                                    0x08000988   Section        0  lcd.o(.text)
    .text                                    0x08004710   Section        0  ad9833.o(.text)
    .text                                    0x08004aec   Section        0  mcp41010.o(.text)
    .text                                    0x08004b84   Section        0  ctiic.o(.text)
    .text                                    0x08004ddc   Section        0  ft5206.o(.text)
    .text                                    0x08005370   Section        0  gt9147.o(.text)
    .text                                    0x08005898   Section        0  ott2001a.o(.text)
    .text                                    0x08005c20   Section        0  touch.o(.text)
    .text                                    0x08006d8c   Section        0  24cxx.o(.text)
    .text                                    0x08006f08   Section        0  myiic.o(.text)
    .text                                    0x08007164   Section        0  ui.o(.text)
    PGA_Set_Amp                              0x08007165   Thumb Code    72  ui.o(.text)
    UI_Update_Hardware_State                 0x080071ad   Thumb Code   144  ui.o(.text)
    UI_Update_Basic_Mode_Data                0x0800723d   Thumb Code   352  ui.o(.text)
    UI_Draw_Basic_Mode_Screen                0x0800739d   Thumb Code   834  ui.o(.text)
    UI_Get_Button_Pressed_Advanced           0x08007825   Thumb Code   130  ui.o(.text)
    UI_Handler_Advanced                      0x080078a7   Thumb Code   790  ui.o(.text)
    UI_Get_Button_Pressed_Basic              0x08007bbd   Thumb Code   372  ui.o(.text)
    UI_Handler_Basic                         0x08007d31   Thumb Code   412  ui.o(.text)
    UI_Draw_Advanced_Mode_Screen             0x08007ee1   Thumb Code   430  ui.o(.text)
    .text                                    0x080080cc   Section        0  control.o(.text)
    .text                                    0x08008108   Section        0  search.o(.text)
    .text                                    0x08008174   Section        0  dsp_process.o(.text)
    Analyze_FFT                              0x0800821b   Thumb Code   208  dsp_process.o(.text)
    find_cutoff_index                        0x080083db   Thumb Code   120  dsp_process.o(.text)
    .text                                    0x08008a3c   Section        0  delay.o(.text)
    .text                                    0x08008b40   Section        0  usart.o(.text)
    .text                                    0x08008c8c   Section       64  startup_stm32f40_41xxx.o(.text)
    $v0                                      0x08008c8c   Number         0  startup_stm32f40_41xxx.o(.text)
    .text                                    0x08008ccc   Section        0  misc.o(.text)
    .text                                    0x08008dac   Section        0  stm32f4xx_gpio.o(.text)
    .text                                    0x08009040   Section        0  stm32f4xx_fsmc.o(.text)
    .text                                    0x08009608   Section        0  stm32f4xx_rcc.o(.text)
    .text                                    0x08009c64   Section        0  stm32f4xx_usart.o(.text)
    .text                                    0x0800a0b8   Section        0  stm32f4xx_tim.o(.text)
    TI4_Config                               0x0800a857   Thumb Code    80  stm32f4xx_tim.o(.text)
    TI3_Config                               0x0800a8b9   Thumb Code    72  stm32f4xx_tim.o(.text)
    TI2_Config                               0x0800a91b   Thumb Code    90  stm32f4xx_tim.o(.text)
    TI1_Config                               0x0800a987   Thumb Code    58  stm32f4xx_tim.o(.text)
    .text                                    0x0800ad5c   Section        0  stm32f4xx_adc.o(.text)
    .text                                    0x0800b1c0   Section        0  stm32f4xx_dma.o(.text)
    .text                                    0x0800b568   Section        0  arm_cfft_radix4_f32.o(.text)
    .text                                    0x0800bc08   Section        0  arm_cfft_radix4_init_f32.o(.text)
    .text                                    0x0800bcc8   Section        0  arm_bitreversal.o(.text)
    .text                                    0x0800beae   Section        2  use_no_semi_2.o(.text)
    .text                                    0x0800beb0   Section        0  noretval__2printf.o(.text)
    .text                                    0x0800bec8   Section        0  noretval__2sprintf.o(.text)
    .text                                    0x0800bef0   Section        0  _printf_str.o(.text)
    .text                                    0x0800bf44   Section        0  _printf_dec.o(.text)
    .text                                    0x0800bfbc   Section        0  _printf_hex_int.o(.text)
    .text                                    0x0800c014   Section        0  __printf_wp.o(.text)
    .text                                    0x0800c122   Section        0  strcat.o(.text)
    .text                                    0x0800c13a   Section       68  rt_memclr.o(.text)
    .text                                    0x0800c17e   Section       78  rt_memclr_w.o(.text)
    .text                                    0x0800c1cc   Section      128  strcmpv7m.o(.text)
    .text                                    0x0800c24c   Section        0  heapauxi.o(.text)
    .text                                    0x0800c252   Section        2  use_no_semi.o(.text)
    .text                                    0x0800c254   Section        0  _rserrno.o(.text)
    .text                                    0x0800c26a   Section        0  _printf_intcommon.o(.text)
    .text                                    0x0800c31c   Section        0  _printf_fp_dec.o(.text)
    _fp_digits                               0x0800c31f   Thumb Code   432  _printf_fp_dec.o(.text)
    .text                                    0x0800c73c   Section        0  _printf_char_common.o(.text)
    _printf_input_char                       0x0800c73d   Thumb Code    10  _printf_char_common.o(.text)
    .text                                    0x0800c76c   Section        0  _sputc.o(.text)
    .text                                    0x0800c776   Section        0  _printf_char.o(.text)
    .text                                    0x0800c7a4   Section        0  _printf_char_file.o(.text)
    .text                                    0x0800c7c8   Section        8  rt_locale_intlibspace.o(.text)
    .text                                    0x0800c7d0   Section        8  rt_errno_addr_intlibspace.o(.text)
    .text                                    0x0800c7d8   Section      138  lludiv10.o(.text)
    .text                                    0x0800c864   Section        0  _printf_fp_infnan.o(.text)
    .text                                    0x0800c8e4   Section        0  bigflt0.o(.text)
    .text                                    0x0800c9c8   Section        0  ferror.o(.text)
    .text                                    0x0800c9d0   Section        8  libspace.o(.text)
    .text                                    0x0800c9d8   Section       74  sys_stackheap_outer.o(.text)
    .text                                    0x0800ca22   Section        0  exit.o(.text)
    CL$$btod_d2e                             0x0800ca34   Section       62  btod.o(CL$$btod_d2e)
    CL$$btod_d2e_denorm_low                  0x0800ca72   Section       70  btod.o(CL$$btod_d2e_denorm_low)
    CL$$btod_d2e_norm_op1                    0x0800cab8   Section       96  btod.o(CL$$btod_d2e_norm_op1)
    CL$$btod_div_common                      0x0800cb18   Section      824  btod.o(CL$$btod_div_common)
    CL$$btod_e2e                             0x0800ce50   Section      220  btod.o(CL$$btod_e2e)
    CL$$btod_ediv                            0x0800cf2c   Section       42  btod.o(CL$$btod_ediv)
    CL$$btod_emul                            0x0800cf56   Section       42  btod.o(CL$$btod_emul)
    CL$$btod_mult_common                     0x0800cf80   Section      580  btod.o(CL$$btod_mult_common)
    i.__ARM_fpclassify                       0x0800d1c4   Section        0  fpclassify.o(i.__ARM_fpclassify)
    i.__hardfp_sqrt                          0x0800d1f4   Section        0  sqrt.o(i.__hardfp_sqrt)
    i._is_digit                              0x0800d26e   Section        0  __printf_wp.o(i._is_digit)
    locale$$code                             0x0800d27c   Section       44  lc_numeric_c.o(locale$$code)
    x$fpl$dadd                               0x0800d2a8   Section      336  daddsub_clz.o(x$fpl$dadd)
    $v0                                      0x0800d2a8   Number         0  daddsub_clz.o(x$fpl$dadd)
    _dadd1                                   0x0800d2b9   Thumb Code     0  daddsub_clz.o(x$fpl$dadd)
    x$fpl$dcmpinf                            0x0800d3f8   Section       24  dcmpi.o(x$fpl$dcmpinf)
    $v0                                      0x0800d3f8   Number         0  dcmpi.o(x$fpl$dcmpinf)
    x$fpl$ddiv                               0x0800d410   Section      688  ddiv.o(x$fpl$ddiv)
    $v0                                      0x0800d410   Number         0  ddiv.o(x$fpl$ddiv)
    ddiv_entry                               0x0800d417   Thumb Code     0  ddiv.o(x$fpl$ddiv)
    x$fpl$dfix                               0x0800d6c0   Section       94  dfix.o(x$fpl$dfix)
    $v0                                      0x0800d6c0   Number         0  dfix.o(x$fpl$dfix)
    x$fpl$dfixu                              0x0800d720   Section       90  dfixu.o(x$fpl$dfixu)
    $v0                                      0x0800d720   Number         0  dfixu.o(x$fpl$dfixu)
    x$fpl$dflt                               0x0800d77a   Section       46  dflt_clz.o(x$fpl$dflt)
    $v0                                      0x0800d77a   Number         0  dflt_clz.o(x$fpl$dflt)
    x$fpl$dfltu                              0x0800d7a8   Section       38  dflt_clz.o(x$fpl$dfltu)
    $v0                                      0x0800d7a8   Number         0  dflt_clz.o(x$fpl$dfltu)
    x$fpl$dleqf                              0x0800d7d0   Section      120  dleqf.o(x$fpl$dleqf)
    $v0                                      0x0800d7d0   Number         0  dleqf.o(x$fpl$dleqf)
    x$fpl$dmul                               0x0800d848   Section      340  dmul.o(x$fpl$dmul)
    $v0                                      0x0800d848   Number         0  dmul.o(x$fpl$dmul)
    x$fpl$dnaninf                            0x0800d99c   Section      156  dnaninf.o(x$fpl$dnaninf)
    $v0                                      0x0800d99c   Number         0  dnaninf.o(x$fpl$dnaninf)
    x$fpl$dretinf                            0x0800da38   Section       12  dretinf.o(x$fpl$dretinf)
    $v0                                      0x0800da38   Number         0  dretinf.o(x$fpl$dretinf)
    x$fpl$drleqf                             0x0800da44   Section      108  drleqf.o(x$fpl$drleqf)
    $v0                                      0x0800da44   Number         0  drleqf.o(x$fpl$drleqf)
    x$fpl$drsb                               0x0800dab0   Section       22  daddsub_clz.o(x$fpl$drsb)
    $v0                                      0x0800dab0   Number         0  daddsub_clz.o(x$fpl$drsb)
    x$fpl$dsqrt                              0x0800dac8   Section      408  dsqrt_umaal.o(x$fpl$dsqrt)
    $v0                                      0x0800dac8   Number         0  dsqrt_umaal.o(x$fpl$dsqrt)
    x$fpl$dsub                               0x0800dc60   Section      468  daddsub_clz.o(x$fpl$dsub)
    $v0                                      0x0800dc60   Number         0  daddsub_clz.o(x$fpl$dsub)
    _dsub1                                   0x0800dc71   Thumb Code     0  daddsub_clz.o(x$fpl$dsub)
    x$fpl$f2d                                0x0800de34   Section       86  f2d.o(x$fpl$f2d)
    $v0                                      0x0800de34   Number         0  f2d.o(x$fpl$f2d)
    x$fpl$fnaninf                            0x0800de8a   Section      140  fnaninf.o(x$fpl$fnaninf)
    $v0                                      0x0800de8a   Number         0  fnaninf.o(x$fpl$fnaninf)
    x$fpl$fpinit                             0x0800df16   Section       10  fpinit.o(x$fpl$fpinit)
    $v0                                      0x0800df16   Number         0  fpinit.o(x$fpl$fpinit)
    x$fpl$printf1                            0x0800df20   Section        4  printf1.o(x$fpl$printf1)
    $v0                                      0x0800df20   Number         0  printf1.o(x$fpl$printf1)
    .constdata                               0x0800df24   Section     6188  lcd.o(.constdata)
    x$fpl$usenofp                            0x0800df24   Section        0  usenofp.o(x$fpl$usenofp)
    .constdata                               0x0800f750   Section       20  ft5206.o(.constdata)
    .constdata                               0x0800f764   Section      194  gt9147.o(.constdata)
    .constdata                               0x0800f826   Section       10  ott2001a.o(.constdata)
    .constdata                               0x0800f830   Section        4  touch.o(.constdata)
    .constdata                               0x0800f838   Section       16  ui.o(.constdata)
    freq_steps                               0x0800f838   Data          16  ui.o(.constdata)
    .constdata                               0x0800f848   Section     1144  search.o(.constdata)
    .constdata                               0x0800fcc0   Section       28  stm32f4xx_fsmc.o(.constdata)
    .constdata                               0x0800fcdc   Section     2048  arm_common_tables.o(.constdata)
    .constdata                               0x080104dc   Section    32768  arm_common_tables.o(.constdata)
    .constdata                               0x080184dc   Section       40  _printf_hex_int.o(.constdata)
    uc_hextab                                0x080184dc   Data          20  _printf_hex_int.o(.constdata)
    lc_hextab                                0x080184f0   Data          20  _printf_hex_int.o(.constdata)
    .constdata                               0x08018504   Section      148  bigflt0.o(.constdata)
    tenpwrs_x                                0x08018504   Data          60  bigflt0.o(.constdata)
    tenpwrs_i                                0x08018540   Data          64  bigflt0.o(.constdata)
    .conststring                             0x08018598   Section      121  touch.o(.conststring)
    locale$$data                             0x08018634   Section       28  lc_numeric_c.o(locale$$data)
    __lcnum_c_name                           0x08018638   Data           2  lc_numeric_c.o(locale$$data)
    __lcnum_c_start                          0x08018640   Data           0  lc_numeric_c.o(locale$$data)
    __lcnum_c_point                          0x0801864c   Data           0  lc_numeric_c.o(locale$$data)
    __lcnum_c_thousands                      0x0801864e   Data           0  lc_numeric_c.o(locale$$data)
    __lcnum_c_grouping                       0x0801864f   Data           0  lc_numeric_c.o(locale$$data)
    __lcnum_c_end                            0x08018650   Data           0  lc_numeric_c.o(locale$$data)
    .data                                    0x20000000   Section        5  main.o(.data)
    .data                                    0x20000008   Section        4  stm32f4xx_it.o(.data)
    tim_irq_count                            0x20000008   Data           4  stm32f4xx_it.o(.data)
    .data                                    0x2000000c   Section       20  system_stm32f4xx.o(.data)
    .data                                    0x20000020   Section        1  key.o(.data)
    key_up                                   0x20000020   Data           1  key.o(.data)
    .data                                    0x20000022   Section        4  lcd.o(.data)
    .data                                    0x20000026   Section        7  ft5206.o(.data)
    t                                        0x2000002c   Data           1  ft5206.o(.data)
    .data                                    0x2000002d   Section        1  gt9147.o(.data)
    t                                        0x2000002d   Data           1  gt9147.o(.data)
    .data                                    0x2000002e   Section        1  ott2001a.o(.data)
    t                                        0x2000002e   Data           1  ott2001a.o(.data)
    .data                                    0x20000030   Section       54  touch.o(.data)
    .data                                    0x20000068   Section       45  ui.o(.data)
    advanced_ui_state                        0x20000068   Data           1  ui.o(.data)
    current_freq                             0x20000070   Data           8  ui.o(.data)
    step_index                               0x20000078   Data           1  ui.o(.data)
    current_step                             0x20000080   Data           8  ui.o(.data)
    is_at_1mhz                               0x20000088   Data           1  ui.o(.data)
    target_v_out_x10                         0x2000008c   Data           4  ui.o(.data)
    direct_amp_value                         0x20000090   Data           4  ui.o(.data)
    control_mode                             0x20000094   Data           1  ui.o(.data)
    .data                                    0x20000095   Section        1  control.o(.data)
    current_mode                             0x20000095   Data           1  control.o(.data)
    .data                                    0x20000098   Section       32  dsp_process.o(.data)
    sweep_index                              0x20000098   Data           4  dsp_process.o(.data)
    final_filter_type                        0x2000009c   Data           1  dsp_process.o(.data)
    pass_band_gain                           0x200000a0   Data           4  dsp_process.o(.data)
    f_cutoff1                                0x200000a4   Data           4  dsp_process.o(.data)
    f_cutoff2                                0x200000a8   Data           4  dsp_process.o(.data)
    f_center                                 0x200000ac   Data           4  dsp_process.o(.data)
    bandwidth                                0x200000b0   Data           4  dsp_process.o(.data)
    q_factor                                 0x200000b4   Data           4  dsp_process.o(.data)
    .data                                    0x200000b8   Section        4  delay.o(.data)
    fac_us                                   0x200000b8   Data           1  delay.o(.data)
    fac_ms                                   0x200000ba   Data           2  delay.o(.data)
    .data                                    0x200000bc   Section        6  usart.o(.data)
    .data                                    0x200000c2   Section       16  stm32f4xx_rcc.o(.data)
    APBAHBPrescTable                         0x200000c2   Data          16  stm32f4xx_rcc.o(.data)
    .bss                                     0x200000d4   Section    91888  main.o(.bss)
    .bss                                     0x200167c4   Section       14  lcd.o(.bss)
    .bss                                     0x200167d4   Section      108  ui.o(.bss)
    btn_freq_p                               0x200167d4   Data          12  ui.o(.bss)
    btn_freq_m                               0x200167e0   Data          12  ui.o(.bss)
    btn_step                                 0x200167ec   Data          12  ui.o(.bss)
    btn_jump_f                               0x200167f8   Data          12  ui.o(.bss)
    btn_jump_a                               0x20016804   Data          12  ui.o(.bss)
    btn_amp_p                                0x20016810   Data          12  ui.o(.bss)
    btn_amp_m                                0x2001681c   Data          12  ui.o(.bss)
    btn_learn                                0x20016828   Data          12  ui.o(.bss)
    btn_reproduce                            0x20016834   Data          12  ui.o(.bss)
    .bss                                     0x20016840   Section    32788  dsp_process.o(.bss)
    fft_inst                                 0x20016840   Data          20  dsp_process.o(.bss)
    fft_buffer                               0x20016854   Data       32768  dsp_process.o(.bss)
    .bss                                     0x2001e854   Section      200  usart.o(.bss)
    .bss                                     0x2001e91c   Section       96  libspace.o(.bss)
    HEAP                                     0x2001e980   Section      512  startup_stm32f40_41xxx.o(HEAP)
    Heap_Mem                                 0x2001e980   Data         512  startup_stm32f40_41xxx.o(HEAP)
    STACK                                    0x2001eb80   Section     1024  startup_stm32f40_41xxx.o(STACK)
    Stack_Mem                                0x2001eb80   Data        1024  startup_stm32f40_41xxx.o(STACK)
    __initial_sp                             0x2001ef80   Data           0  startup_stm32f40_41xxx.o(STACK)

    Global Symbols

    Symbol Name                              Value     Ov Type        Size  Object(Section)

    BuildAttributes$$THM_ISAv4$E$P$D$K$B$S$7EM$VFPi3$EXTD16$VFPS$VFMA$PE$A:L22UL41UL21$X:L11$S22US41US21$IEEE1$IW$USESV6$~STKCKD$USESV7$~SHL$OTIME$ROPI$IEEEX$EBA8$UX$STANDARDLIB$REQ8$PRES8$EABIv2 0x00000000   Number         0  anon$$obj.o ABSOLUTE
    __ARM_use_no_argv                        0x00000000   Number         0  main.o ABSOLUTE
    _printf_flags                            0x00000000   Number         0  printf_stubs.o ABSOLUTE
    _printf_return_value                     0x00000000   Number         0  printf_stubs.o ABSOLUTE
    _printf_sizespec                         0x00000000   Number         0  printf_stubs.o ABSOLUTE
    _printf_widthprec                        0x00000000   Number         0  printf_stubs.o ABSOLUTE
    __ARM_exceptions_init                     - Undefined Weak Reference
    __alloca_initialize                       - Undefined Weak Reference
    __arm_fini_                               - Undefined Weak Reference
    __arm_preinit_                            - Undefined Weak Reference
    __cpp_initialize__aeabi_                  - Undefined Weak Reference
    __cxa_finalize                            - Undefined Weak Reference
    __sigvec_lookup                           - Undefined Weak Reference
    _atexit_init                              - Undefined Weak Reference
    _call_atexit_fns                          - Undefined Weak Reference
    _clock_init                               - Undefined Weak Reference
    _fp_trap_init                             - Undefined Weak Reference
    _fp_trap_shutdown                         - Undefined Weak Reference
    _get_lc_collate                           - Undefined Weak Reference
    _get_lc_ctype                             - Undefined Weak Reference
    _get_lc_monetary                          - Undefined Weak Reference
    _get_lc_time                              - Undefined Weak Reference
    _getenv_init                              - Undefined Weak Reference
    _handle_redirection                       - Undefined Weak Reference
    _init_alloc                               - Undefined Weak Reference
    _init_user_alloc                          - Undefined Weak Reference
    _initio                                   - Undefined Weak Reference
    _mutex_acquire                            - Undefined Weak Reference
    _mutex_release                            - Undefined Weak Reference
    _printf_mbtowc                            - Undefined Weak Reference
    _printf_post_padding                      - Undefined Weak Reference
    _printf_pre_padding                       - Undefined Weak Reference
    _printf_truncate_signed                   - Undefined Weak Reference
    _printf_truncate_unsigned                 - Undefined Weak Reference
    _rand_init                                - Undefined Weak Reference
    _signal_finish                            - Undefined Weak Reference
    _signal_init                              - Undefined Weak Reference
    _terminate_alloc                          - Undefined Weak Reference
    _terminate_user_alloc                     - Undefined Weak Reference
    _terminateio                              - Undefined Weak Reference
    __Vectors_Size                           0x00000188   Number         0  startup_stm32f40_41xxx.o ABSOLUTE
    __Vectors                                0x08000000   Data           4  startup_stm32f40_41xxx.o(RESET)
    __Vectors_End                            0x08000188   Data           0  startup_stm32f40_41xxx.o(RESET)
    __main                                   0x08000189   Thumb Code     8  __main.o(!!!main)
    __scatterload                            0x08000191   Thumb Code     0  __scatter.o(!!!scatter)
    __scatterload_rt2                        0x08000191   Thumb Code    44  __scatter.o(!!!scatter)
    __scatterload_rt2_thumb_only             0x08000191   Thumb Code     0  __scatter.o(!!!scatter)
    __scatterload_null                       0x0800019f   Thumb Code     0  __scatter.o(!!!scatter)
    __scatterload_copy                       0x080001c5   Thumb Code    26  __scatter_copy.o(!!handler_copy)
    __scatterload_zeroinit                   0x080001e1   Thumb Code    28  __scatter_zi.o(!!handler_zi)
    _printf_f                                0x080001fd   Thumb Code     0  _printf_f.o(.ARM.Collect$$_printf_percent$$00000003)
    _printf_percent                          0x080001fd   Thumb Code     0  _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000)
    _printf_d                                0x08000203   Thumb Code     0  _printf_d.o(.ARM.Collect$$_printf_percent$$00000009)
    _printf_x                                0x08000209   Thumb Code     0  _printf_x.o(.ARM.Collect$$_printf_percent$$0000000C)
    _printf_s                                0x0800020f   Thumb Code     0  _printf_s.o(.ARM.Collect$$_printf_percent$$00000014)
    _printf_percent_end                      0x08000215   Thumb Code     0  _printf_percent_end.o(.ARM.Collect$$_printf_percent$$00000017)
    __rt_lib_init                            0x08000219   Thumb Code     0  libinit.o(.ARM.Collect$$libinit$$00000000)
    __rt_lib_init_fp_1                       0x0800021b   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000001)
    __rt_lib_init_heap_1                     0x0800021f   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000000A)
    __rt_lib_init_lc_common                  0x0800021f   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000000F)
    __rt_lib_init_preinit_1                  0x0800021f   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000004)
    __rt_lib_init_rand_1                     0x0800021f   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000000E)
    __rt_lib_init_user_alloc_1               0x0800021f   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000000C)
    __rt_lib_init_lc_collate_1               0x08000225   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000011)
    __rt_lib_init_lc_ctype_1                 0x08000225   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000013)
    __rt_lib_init_lc_monetary_1              0x08000225   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000015)
    __rt_lib_init_lc_numeric_2               0x08000225   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000016)
    __rt_lib_init_alloca_1                   0x0800022f   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000002E)
    __rt_lib_init_argv_1                     0x0800022f   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000002C)
    __rt_lib_init_atexit_1                   0x0800022f   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000001B)
    __rt_lib_init_clock_1                    0x0800022f   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000021)
    __rt_lib_init_cpp_1                      0x0800022f   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000032)
    __rt_lib_init_exceptions_1               0x0800022f   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000030)
    __rt_lib_init_fp_trap_1                  0x0800022f   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000001F)
    __rt_lib_init_getenv_1                   0x0800022f   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000023)
    __rt_lib_init_lc_numeric_1               0x0800022f   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000017)
    __rt_lib_init_lc_time_1                  0x0800022f   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000019)
    __rt_lib_init_return                     0x0800022f   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000033)
    __rt_lib_init_signal_1                   0x0800022f   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000001D)
    __rt_lib_init_stdio_1                    0x0800022f   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000025)
    __rt_lib_shutdown                        0x08000231   Thumb Code     0  libshutdown.o(.ARM.Collect$$libshutdown$$00000000)
    __rt_lib_shutdown_cpp_1                  0x08000233   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000004)
    __rt_lib_shutdown_fini_1                 0x08000233   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000002)
    __rt_lib_shutdown_fp_trap_1              0x08000233   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000009)
    __rt_lib_shutdown_heap_1                 0x08000233   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000011)
    __rt_lib_shutdown_return                 0x08000233   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000012)
    __rt_lib_shutdown_signal_1               0x08000233   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$0000000C)
    __rt_lib_shutdown_stdio_1                0x08000233   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000006)
    __rt_lib_shutdown_user_alloc_1           0x08000233   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$0000000E)
    __rt_entry                               0x08000235   Thumb Code     0  __rtentry.o(.ARM.Collect$$rtentry$$00000000)
    __rt_entry_presh_1                       0x08000235   Thumb Code     0  __rtentry2.o(.ARM.Collect$$rtentry$$00000002)
    __rt_entry_sh                            0x08000235   Thumb Code     0  __rtentry4.o(.ARM.Collect$$rtentry$$00000004)
    __rt_entry_li                            0x0800023b   Thumb Code     0  __rtentry2.o(.ARM.Collect$$rtentry$$0000000A)
    __rt_entry_postsh_1                      0x0800023b   Thumb Code     0  __rtentry2.o(.ARM.Collect$$rtentry$$00000009)
    __rt_entry_main                          0x0800023f   Thumb Code     0  __rtentry2.o(.ARM.Collect$$rtentry$$0000000D)
    __rt_entry_postli_1                      0x0800023f   Thumb Code     0  __rtentry2.o(.ARM.Collect$$rtentry$$0000000C)
    __rt_exit                                0x08000247   Thumb Code     0  rtexit.o(.ARM.Collect$$rtexit$$00000000)
    __rt_exit_ls                             0x08000249   Thumb Code     0  rtexit2.o(.ARM.Collect$$rtexit$$00000003)
    __rt_exit_prels_1                        0x08000249   Thumb Code     0  rtexit2.o(.ARM.Collect$$rtexit$$00000002)
    __rt_exit_exit                           0x0800024d   Thumb Code     0  rtexit2.o(.ARM.Collect$$rtexit$$00000004)
    main                                     0x08000255   Thumb Code   126  main.o(.text)
    TIM1_UP_TIM10_IRQHandler                 0x0800032d   Thumb Code    64  stm32f4xx_it.o(.text)
    DMA2_Stream0_IRQHandler                  0x0800036d   Thumb Code   190  stm32f4xx_it.o(.text)
    NMI_Handler                              0x0800042b   Thumb Code     2  stm32f4xx_it.o(.text)
    HardFault_Handler                        0x0800042d   Thumb Code     4  stm32f4xx_it.o(.text)
    MemManage_Handler                        0x08000431   Thumb Code     4  stm32f4xx_it.o(.text)
    BusFault_Handler                         0x08000435   Thumb Code     4  stm32f4xx_it.o(.text)
    UsageFault_Handler                       0x08000439   Thumb Code     4  stm32f4xx_it.o(.text)
    SVC_Handler                              0x0800043d   Thumb Code     2  stm32f4xx_it.o(.text)
    DebugMon_Handler                         0x0800043f   Thumb Code     2  stm32f4xx_it.o(.text)
    PendSV_Handler                           0x08000441   Thumb Code     2  stm32f4xx_it.o(.text)
    SysTick_Handler                          0x08000443   Thumb Code     2  stm32f4xx_it.o(.text)
    SystemInit                               0x08000565   Thumb Code    88  system_stm32f4xx.o(.text)
    SystemCoreClockUpdate                    0x080005bd   Thumb Code   174  system_stm32f4xx.o(.text)
    LED_Init                                 0x08000699   Thumb Code    60  led.o(.text)
    KEY_Init                                 0x080006d9   Thumb Code    60  key.o(.text)
    KEY_Scan                                 0x08000715   Thumb Code   114  key.o(.text)
    TIM3_Int_Init                            0x08000795   Thumb Code    88  timer.o(.text)
    TIM3_IRQHandler                          0x080007ed   Thumb Code    20  timer.o(.text)
    ADC_GPIO_Init                            0x08000805   Thumb Code    36  adc_dma_timer.o(.text)
    ADC_Trig_Timer_Init                      0x08000829   Thumb Code    58  adc_dma_timer.o(.text)
    ADC_Config                               0x08000863   Thumb Code   260  adc_dma_timer.o(.text)
    LCD_WR_REG                               0x08000989   Thumb Code    18  lcd.o(.text)
    LCD_WR_DATA                              0x0800099b   Thumb Code    20  lcd.o(.text)
    LCD_RD_DATA                              0x080009af   Thumb Code    16  lcd.o(.text)
    LCD_WriteReg                             0x080009bf   Thumb Code    14  lcd.o(.text)
    LCD_ReadReg                              0x080009cd   Thumb Code    22  lcd.o(.text)
    LCD_WriteRAM_Prepare                     0x080009e3   Thumb Code    10  lcd.o(.text)
    LCD_WriteRAM                             0x080009ed   Thumb Code     8  lcd.o(.text)
    LCD_BGR2RGB                              0x080009f5   Thumb Code    26  lcd.o(.text)
    opt_delay                                0x08000a0f   Thumb Code    14  lcd.o(.text)
    LCD_SetCursor                            0x08000a1d   Thumb Code   282  lcd.o(.text)
    LCD_ReadPoint                            0x08000b37   Thumb Code   148  lcd.o(.text)
    LCD_DisplayOn                            0x08000bcb   Thumb Code    32  lcd.o(.text)
    LCD_DisplayOff                           0x08000beb   Thumb Code    32  lcd.o(.text)
    LCD_Scan_Dir                             0x08000c0b   Thumb Code   582  lcd.o(.text)
    LCD_DrawPoint                            0x08000e51   Thumb Code    28  lcd.o(.text)
    LCD_Fast_DrawPoint                       0x08000e6d   Thumb Code   238  lcd.o(.text)
    LCD_SSD_BackLightSet                     0x08000f5b   Thumb Code    88  lcd.o(.text)
    LCD_Display_Dir                          0x08000fb3   Thumb Code   360  lcd.o(.text)
    LCD_Set_Window                           0x0800111b   Thumb Code   384  lcd.o(.text)
    LCD_Clear                                0x0800129b   Thumb Code    42  lcd.o(.text)
    LCD_Init                                 0x080012c5   Thumb Code 11808  lcd.o(.text)
    LCD_Fill                                 0x080040e5   Thumb Code    82  lcd.o(.text)
    LCD_Color_Fill                           0x08004137   Thumb Code    92  lcd.o(.text)
    LCD_DrawLine                             0x08004193   Thumb Code   176  lcd.o(.text)
    LCD_DrawRectangle                        0x08004243   Thumb Code    60  lcd.o(.text)
    LCD_Draw_Circle                          0x0800427f   Thumb Code   152  lcd.o(.text)
    LCD_ShowChar                             0x08004317   Thumb Code   284  lcd.o(.text)
    LCD_Pow                                  0x08004433   Thumb Code    22  lcd.o(.text)
    LCD_ShowNum                              0x08004449   Thumb Code   148  lcd.o(.text)
    LCD_ShowxNum                             0x080044dd   Thumb Code   190  lcd.o(.text)
    LCD_ShowString                           0x0800459b   Thumb Code   102  lcd.o(.text)
    Chinese_Show_one                         0x08004601   Thumb Code   176  lcd.o(.text)
    show_picture                             0x080046b1   Thumb Code    66  lcd.o(.text)
    SPI1_Init                                0x08004711   Thumb Code   100  ad9833.o(.text)
    AD9833_Write_1                           0x08004775   Thumb Code    92  ad9833.o(.text)
    AD9833_Write_2                           0x080047d1   Thumb Code    96  ad9833.o(.text)
    AD9833_1_WaveSeting                      0x08004831   Thumb Code   220  ad9833.o(.text)
    AD9833_2_WaveSeting                      0x0800490d   Thumb Code   220  ad9833.o(.text)
    AD9833_Sweep_Fre                         0x080049e9   Thumb Code   224  ad9833.o(.text)
    AD9833_AmpSet_1                          0x08004aed   Thumb Code    72  mcp41010.o(.text)
    AD9833_AmpSet_2                          0x08004b35   Thumb Code    76  mcp41010.o(.text)
    CT_Delay                                 0x08004b85   Thumb Code    10  ctiic.o(.text)
    CT_IIC_Init                              0x08004b8f   Thumb Code    60  ctiic.o(.text)
    CT_IIC_Start                             0x08004bcb   Thumb Code    62  ctiic.o(.text)
    CT_IIC_Stop                              0x08004c09   Thumb Code    56  ctiic.o(.text)
    CT_IIC_Wait_Ack                          0x08004c41   Thumb Code    78  ctiic.o(.text)
    CT_IIC_Ack                               0x08004c8f   Thumb Code    66  ctiic.o(.text)
    CT_IIC_NAck                              0x08004cd1   Thumb Code    66  ctiic.o(.text)
    CT_IIC_Send_Byte                         0x08004d13   Thumb Code    82  ctiic.o(.text)
    CT_IIC_Read_Byte                         0x08004d65   Thumb Code    96  ctiic.o(.text)
    FT5206_WR_Reg                            0x08004ddd   Thumb Code    82  ft5206.o(.text)
    FT5206_RD_Reg                            0x08004e2f   Thumb Code    88  ft5206.o(.text)
    FT5206_Init                              0x08004e87   Thumb Code   316  ft5206.o(.text)
    FT5206_Scan                              0x08004fc3   Thumb Code   934  ft5206.o(.text)
    GT9147_WR_Reg                            0x08005371   Thumb Code    92  gt9147.o(.text)
    GT9147_Send_Cfg                          0x080053cd   Thumb Code    86  gt9147.o(.text)
    GT9147_RD_Reg                            0x08005423   Thumb Code    98  gt9147.o(.text)
    GT9147_Init                              0x08005485   Thumb Code   292  gt9147.o(.text)
    GT9147_Scan                              0x080055a9   Thumb Code   744  gt9147.o(.text)
    OTT2001A_WR_Reg                          0x08005899   Thumb Code    92  ott2001a.o(.text)
    OTT2001A_RD_Reg                          0x080058f5   Thumb Code    98  ott2001a.o(.text)
    OTT2001A_SensorControl                   0x08005957   Thumb Code    28  ott2001a.o(.text)
    OTT2001A_Init                            0x08005973   Thumb Code   142  ott2001a.o(.text)
    OTT2001A_Scan                            0x08005a01   Thumb Code   478  ott2001a.o(.text)
    TP_Write_Byte                            0x08005c21   Thumb Code    64  touch.o(.text)
    TP_Read_AD                               0x08005c61   Thumb Code   132  touch.o(.text)
    TP_Read_XOY                              0x08005ce5   Thumb Code   120  touch.o(.text)
    TP_Read_XY                               0x08005d5d   Thumb Code    38  touch.o(.text)
    TP_Read_XY2                              0x08005d83   Thumb Code   192  touch.o(.text)
    TP_Drow_Touch_Point                      0x08005e43   Thumb Code   108  touch.o(.text)
    TP_Draw_Big_Point                        0x08005eaf   Thumb Code    54  touch.o(.text)
    TP_Scan                                  0x08005ee5   Thumb Code   244  touch.o(.text)
    TP_Save_Adjdata                          0x08005fd9   Thumb Code   166  touch.o(.text)
    TP_Get_Adjdata                           0x0800607f   Thumb Code   160  touch.o(.text)
    TP_Adj_Info_Show                         0x0800611f   Thumb Code   374  touch.o(.text)
    TP_Adjust                                0x08006295   Thumb Code  2290  touch.o(.text)
    TP_Init                                  0x08006b87   Thumb Code   428  touch.o(.text)
    AT24CXX_Init                             0x08006d8d   Thumb Code     8  24cxx.o(.text)
    AT24CXX_ReadOneByte                      0x08006d95   Thumb Code    88  24cxx.o(.text)
    AT24CXX_WriteOneByte                     0x08006ded   Thumb Code    80  24cxx.o(.text)
    AT24CXX_WriteLenByte                     0x08006e3d   Thumb Code    42  24cxx.o(.text)
    AT24CXX_ReadLenByte                      0x08006e67   Thumb Code    44  24cxx.o(.text)
    AT24CXX_Check                            0x08006e93   Thumb Code    46  24cxx.o(.text)
    AT24CXX_Read                             0x08006ec1   Thumb Code    34  24cxx.o(.text)
    AT24CXX_Write                            0x08006ee3   Thumb Code    36  24cxx.o(.text)
    IIC_Init                                 0x08006f09   Thumb Code    62  myiic.o(.text)
    IIC_Start                                0x08006f47   Thumb Code    68  myiic.o(.text)
    IIC_Stop                                 0x08006f8b   Thumb Code    62  myiic.o(.text)
    IIC_Wait_Ack                             0x08006fc9   Thumb Code    82  myiic.o(.text)
    IIC_Ack                                  0x0800701b   Thumb Code    66  myiic.o(.text)
    IIC_NAck                                 0x0800705d   Thumb Code    66  myiic.o(.text)
    IIC_Send_Byte                            0x0800709f   Thumb Code    90  myiic.o(.text)
    IIC_Read_Byte                            0x080070f9   Thumb Code    94  myiic.o(.text)
    UI_Init                                  0x080076df   Thumb Code   326  ui.o(.text)
    UI_Handler                               0x08007ecd   Thumb Code    20  ui.o(.text)
    UI_Mode_Switch                           0x0800808f   Thumb Code    26  ui.o(.text)
    Control_Init                             0x080080cd   Thumb Code     8  control.o(.text)
    Control_Get_Mode                         0x080080d5   Thumb Code     6  control.o(.text)
    Control_Handler                          0x080080db   Thumb Code    40  control.o(.text)
    Search_Get_Best_Amp                      0x08008109   Thumb Code    96  search.o(.text)
    DSP_Init                                 0x08008175   Thumb Code    24  dsp_process.o(.text)
    Perform_Frequency_Sweep_Start            0x0800818d   Thumb Code   142  dsp_process.o(.text)
    Perform_Frequency_Sweep_Step_From_IRQ    0x080082eb   Thumb Code   240  dsp_process.o(.text)
    Median_Filter                            0x08008453   Thumb Code   476  dsp_process.o(.text)
    Analyze_Sweep_Result                     0x0800862f   Thumb Code   798  dsp_process.o(.text)
    DSP_Is_Sweeping                          0x0800894d   Thumb Code    16  dsp_process.o(.text)
    DSP_Get_Learned_Type                     0x0800895d   Thumb Code     6  dsp_process.o(.text)
    DSP_Get_PassBand_Gain                    0x08008963   Thumb Code     8  dsp_process.o(.text)
    DSP_Get_Cutoff_Freq1                     0x0800896b   Thumb Code     8  dsp_process.o(.text)
    DSP_Get_Cutoff_Freq2                     0x08008973   Thumb Code     8  dsp_process.o(.text)
    DSP_Get_Center_Freq                      0x0800897b   Thumb Code     8  dsp_process.o(.text)
    DSP_Get_Bandwidth                        0x08008983   Thumb Code     8  dsp_process.o(.text)
    DSP_Get_Q_Factor                         0x0800898b   Thumb Code     8  dsp_process.o(.text)
    delay_init                               0x08008a3d   Thumb Code    52  delay.o(.text)
    delay_us                                 0x08008a71   Thumb Code    72  delay.o(.text)
    delay_xms                                0x08008ab9   Thumb Code    72  delay.o(.text)
    delay_ms                                 0x08008b01   Thumb Code    56  delay.o(.text)
    _sys_exit                                0x08008b41   Thumb Code     6  usart.o(.text)
    fputc                                    0x08008b47   Thumb Code    22  usart.o(.text)
    uart_init                                0x08008b5d   Thumb Code   164  usart.o(.text)
    USART1_IRQHandler                        0x08008c01   Thumb Code   122  usart.o(.text)
    Reset_Handler                            0x08008c8d   Thumb Code     8  startup_stm32f40_41xxx.o(.text)
    ADC_IRQHandler                           0x08008ca7   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    CAN1_RX0_IRQHandler                      0x08008ca7   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    CAN1_RX1_IRQHandler                      0x08008ca7   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    CAN1_SCE_IRQHandler                      0x08008ca7   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    CAN1_TX_IRQHandler                       0x08008ca7   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    CAN2_RX0_IRQHandler                      0x08008ca7   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    CAN2_RX1_IRQHandler                      0x08008ca7   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    CAN2_SCE_IRQHandler                      0x08008ca7   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    CAN2_TX_IRQHandler                       0x08008ca7   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    CRYP_IRQHandler                          0x08008ca7   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    DCMI_IRQHandler                          0x08008ca7   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    DMA1_Stream0_IRQHandler                  0x08008ca7   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    DMA1_Stream1_IRQHandler                  0x08008ca7   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    DMA1_Stream2_IRQHandler                  0x08008ca7   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    DMA1_Stream3_IRQHandler                  0x08008ca7   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    DMA1_Stream4_IRQHandler                  0x08008ca7   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    DMA1_Stream5_IRQHandler                  0x08008ca7   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    DMA1_Stream6_IRQHandler                  0x08008ca7   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    DMA1_Stream7_IRQHandler                  0x08008ca7   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    DMA2_Stream1_IRQHandler                  0x08008ca7   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    DMA2_Stream2_IRQHandler                  0x08008ca7   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    DMA2_Stream3_IRQHandler                  0x08008ca7   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    DMA2_Stream4_IRQHandler                  0x08008ca7   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    DMA2_Stream5_IRQHandler                  0x08008ca7   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    DMA2_Stream6_IRQHandler                  0x08008ca7   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    DMA2_Stream7_IRQHandler                  0x08008ca7   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    ETH_IRQHandler                           0x08008ca7   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    ETH_WKUP_IRQHandler                      0x08008ca7   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    EXTI0_IRQHandler                         0x08008ca7   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    EXTI15_10_IRQHandler                     0x08008ca7   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    EXTI1_IRQHandler                         0x08008ca7   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    EXTI2_IRQHandler                         0x08008ca7   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    EXTI3_IRQHandler                         0x08008ca7   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    EXTI4_IRQHandler                         0x08008ca7   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    EXTI9_5_IRQHandler                       0x08008ca7   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    FLASH_IRQHandler                         0x08008ca7   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    FPU_IRQHandler                           0x08008ca7   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    FSMC_IRQHandler                          0x08008ca7   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    HASH_RNG_IRQHandler                      0x08008ca7   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    I2C1_ER_IRQHandler                       0x08008ca7   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    I2C1_EV_IRQHandler                       0x08008ca7   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    I2C2_ER_IRQHandler                       0x08008ca7   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    I2C2_EV_IRQHandler                       0x08008ca7   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    I2C3_ER_IRQHandler                       0x08008ca7   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    I2C3_EV_IRQHandler                       0x08008ca7   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    OTG_FS_IRQHandler                        0x08008ca7   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    OTG_FS_WKUP_IRQHandler                   0x08008ca7   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    OTG_HS_EP1_IN_IRQHandler                 0x08008ca7   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    OTG_HS_EP1_OUT_IRQHandler                0x08008ca7   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    OTG_HS_IRQHandler                        0x08008ca7   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    OTG_HS_WKUP_IRQHandler                   0x08008ca7   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    PVD_IRQHandler                           0x08008ca7   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    RCC_IRQHandler                           0x08008ca7   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    RTC_Alarm_IRQHandler                     0x08008ca7   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    RTC_WKUP_IRQHandler                      0x08008ca7   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    SDIO_IRQHandler                          0x08008ca7   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    SPI1_IRQHandler                          0x08008ca7   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    SPI2_IRQHandler                          0x08008ca7   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    SPI3_IRQHandler                          0x08008ca7   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    TAMP_STAMP_IRQHandler                    0x08008ca7   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    TIM1_BRK_TIM9_IRQHandler                 0x08008ca7   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    TIM1_CC_IRQHandler                       0x08008ca7   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    TIM1_TRG_COM_TIM11_IRQHandler            0x08008ca7   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    TIM2_IRQHandler                          0x08008ca7   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    TIM4_IRQHandler                          0x08008ca7   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    TIM5_IRQHandler                          0x08008ca7   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    TIM6_DAC_IRQHandler                      0x08008ca7   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    TIM7_IRQHandler                          0x08008ca7   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    TIM8_BRK_TIM12_IRQHandler                0x08008ca7   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    TIM8_CC_IRQHandler                       0x08008ca7   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    TIM8_TRG_COM_TIM14_IRQHandler            0x08008ca7   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    TIM8_UP_TIM13_IRQHandler                 0x08008ca7   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    UART4_IRQHandler                         0x08008ca7   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    UART5_IRQHandler                         0x08008ca7   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    USART2_IRQHandler                        0x08008ca7   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    USART3_IRQHandler                        0x08008ca7   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    USART6_IRQHandler                        0x08008ca7   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    WWDG_IRQHandler                          0x08008ca7   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    __user_initial_stackheap                 0x08008ca9   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    NVIC_PriorityGroupConfig                 0x08008ccd   Thumb Code    10  misc.o(.text)
    NVIC_Init                                0x08008cd7   Thumb Code   106  misc.o(.text)
    NVIC_SetVectorTable                      0x08008d41   Thumb Code    14  misc.o(.text)
    NVIC_SystemLPConfig                      0x08008d4f   Thumb Code    34  misc.o(.text)
    SysTick_CLKSourceConfig                  0x08008d71   Thumb Code    40  misc.o(.text)
    GPIO_DeInit                              0x08008dad   Thumb Code   268  stm32f4xx_gpio.o(.text)
    GPIO_Init                                0x08008eb9   Thumb Code   144  stm32f4xx_gpio.o(.text)
    GPIO_StructInit                          0x08008f49   Thumb Code    18  stm32f4xx_gpio.o(.text)
    GPIO_PinLockConfig                       0x08008f5b   Thumb Code    34  stm32f4xx_gpio.o(.text)
    GPIO_ReadInputDataBit                    0x08008f7d   Thumb Code    18  stm32f4xx_gpio.o(.text)
    GPIO_ReadInputData                       0x08008f8f   Thumb Code     8  stm32f4xx_gpio.o(.text)
    GPIO_ReadOutputDataBit                   0x08008f97   Thumb Code    18  stm32f4xx_gpio.o(.text)
    GPIO_ReadOutputData                      0x08008fa9   Thumb Code     8  stm32f4xx_gpio.o(.text)
    GPIO_SetBits                             0x08008fb1   Thumb Code     4  stm32f4xx_gpio.o(.text)
    GPIO_ResetBits                           0x08008fb5   Thumb Code     4  stm32f4xx_gpio.o(.text)
    GPIO_WriteBit                            0x08008fb9   Thumb Code    10  stm32f4xx_gpio.o(.text)
    GPIO_Write                               0x08008fc3   Thumb Code     4  stm32f4xx_gpio.o(.text)
    GPIO_ToggleBits                          0x08008fc7   Thumb Code     8  stm32f4xx_gpio.o(.text)
    GPIO_PinAFConfig                         0x08008fcf   Thumb Code    70  stm32f4xx_gpio.o(.text)
    FSMC_NORSRAMDeInit                       0x08009041   Thumb Code    54  stm32f4xx_fsmc.o(.text)
    FSMC_NORSRAMInit                         0x08009077   Thumb Code   230  stm32f4xx_fsmc.o(.text)
    FSMC_NORSRAMStructInit                   0x0800915d   Thumb Code    48  stm32f4xx_fsmc.o(.text)
    FSMC_NORSRAMCmd                          0x0800918d   Thumb Code    46  stm32f4xx_fsmc.o(.text)
    FSMC_NANDDeInit                          0x080091bb   Thumb Code    62  stm32f4xx_fsmc.o(.text)
    FSMC_NANDInit                            0x080091f9   Thumb Code   132  stm32f4xx_fsmc.o(.text)
    FSMC_NANDStructInit                      0x0800927d   Thumb Code    54  stm32f4xx_fsmc.o(.text)
    FSMC_NANDCmd                             0x080092b3   Thumb Code    86  stm32f4xx_fsmc.o(.text)
    FSMC_NANDECCCmd                          0x08009309   Thumb Code    86  stm32f4xx_fsmc.o(.text)
    FSMC_GetECC                              0x0800935f   Thumb Code    24  stm32f4xx_fsmc.o(.text)
    FSMC_PCCARDDeInit                        0x08009377   Thumb Code    36  stm32f4xx_fsmc.o(.text)
    FSMC_PCCARDInit                          0x0800939b   Thumb Code   130  stm32f4xx_fsmc.o(.text)
    FSMC_PCCARDStructInit                    0x0800941d   Thumb Code    60  stm32f4xx_fsmc.o(.text)
    FSMC_PCCARDCmd                           0x08009459   Thumb Code    44  stm32f4xx_fsmc.o(.text)
    FSMC_ITConfig                            0x08009485   Thumb Code   128  stm32f4xx_fsmc.o(.text)
    FSMC_GetFlagStatus                       0x08009505   Thumb Code    54  stm32f4xx_fsmc.o(.text)
    FSMC_ClearFlag                           0x0800953b   Thumb Code    74  stm32f4xx_fsmc.o(.text)
    FSMC_GetITStatus                         0x08009585   Thumb Code    62  stm32f4xx_fsmc.o(.text)
    FSMC_ClearITPendingBit                   0x080095c3   Thumb Code    66  stm32f4xx_fsmc.o(.text)
    RCC_DeInit                               0x08009609   Thumb Code    82  stm32f4xx_rcc.o(.text)
    RCC_HSEConfig                            0x0800965b   Thumb Code    14  stm32f4xx_rcc.o(.text)
    RCC_GetFlagStatus                        0x08009669   Thumb Code    60  stm32f4xx_rcc.o(.text)
    RCC_WaitForHSEStartUp                    0x080096a5   Thumb Code    56  stm32f4xx_rcc.o(.text)
    RCC_AdjustHSICalibrationValue            0x080096dd   Thumb Code    20  stm32f4xx_rcc.o(.text)
    RCC_HSICmd                               0x080096f1   Thumb Code     6  stm32f4xx_rcc.o(.text)
    RCC_LSEConfig                            0x080096f7   Thumb Code    46  stm32f4xx_rcc.o(.text)
    RCC_LSICmd                               0x08009725   Thumb Code     6  stm32f4xx_rcc.o(.text)
    RCC_PLLConfig                            0x0800972b   Thumb Code    32  stm32f4xx_rcc.o(.text)
    RCC_PLLCmd                               0x0800974b   Thumb Code     6  stm32f4xx_rcc.o(.text)
    RCC_PLLI2SConfig                         0x08009751   Thumb Code    14  stm32f4xx_rcc.o(.text)
    RCC_PLLI2SCmd                            0x0800975f   Thumb Code     6  stm32f4xx_rcc.o(.text)
    RCC_PLLSAIConfig                         0x08009765   Thumb Code    20  stm32f4xx_rcc.o(.text)
    RCC_PLLSAICmd                            0x08009779   Thumb Code     6  stm32f4xx_rcc.o(.text)
    RCC_ClockSecuritySystemCmd               0x0800977f   Thumb Code     6  stm32f4xx_rcc.o(.text)
    RCC_MCO1Config                           0x08009785   Thumb Code    28  stm32f4xx_rcc.o(.text)
    RCC_MCO2Config                           0x080097a1   Thumb Code    28  stm32f4xx_rcc.o(.text)
    RCC_SYSCLKConfig                         0x080097bd   Thumb Code    20  stm32f4xx_rcc.o(.text)
    RCC_GetSYSCLKSource                      0x080097d1   Thumb Code    12  stm32f4xx_rcc.o(.text)
    RCC_HCLKConfig                           0x080097dd   Thumb Code    20  stm32f4xx_rcc.o(.text)
    RCC_PCLK1Config                          0x080097f1   Thumb Code    20  stm32f4xx_rcc.o(.text)
    RCC_PCLK2Config                          0x08009805   Thumb Code    22  stm32f4xx_rcc.o(.text)
    RCC_GetClocksFreq                        0x0800981b   Thumb Code   222  stm32f4xx_rcc.o(.text)
    RCC_RTCCLKConfig                         0x080098f9   Thumb Code    54  stm32f4xx_rcc.o(.text)
    RCC_RTCCLKCmd                            0x0800992f   Thumb Code     8  stm32f4xx_rcc.o(.text)
    RCC_BackupResetCmd                       0x08009937   Thumb Code     8  stm32f4xx_rcc.o(.text)
    RCC_I2SCLKConfig                         0x0800993f   Thumb Code     6  stm32f4xx_rcc.o(.text)
    RCC_SAIPLLI2SClkDivConfig                0x08009945   Thumb Code    26  stm32f4xx_rcc.o(.text)
    RCC_SAIPLLSAIClkDivConfig                0x0800995f   Thumb Code    28  stm32f4xx_rcc.o(.text)
    RCC_SAIBlockACLKConfig                   0x0800997b   Thumb Code    20  stm32f4xx_rcc.o(.text)
    RCC_SAIBlockBCLKConfig                   0x0800998f   Thumb Code    20  stm32f4xx_rcc.o(.text)
    RCC_LTDCCLKDivConfig                     0x080099a3   Thumb Code    20  stm32f4xx_rcc.o(.text)
    RCC_TIMCLKPresConfig                     0x080099b7   Thumb Code     6  stm32f4xx_rcc.o(.text)
    RCC_AHB1PeriphClockCmd                   0x080099bd   Thumb Code    34  stm32f4xx_rcc.o(.text)
    RCC_AHB2PeriphClockCmd                   0x080099df   Thumb Code    78  stm32f4xx_rcc.o(.text)
    RCC_AHB3PeriphClockCmd                   0x08009a2d   Thumb Code    34  stm32f4xx_rcc.o(.text)
    RCC_APB1PeriphClockCmd                   0x08009a4f   Thumb Code    34  stm32f4xx_rcc.o(.text)
    RCC_APB2PeriphClockCmd                   0x08009a71   Thumb Code    34  stm32f4xx_rcc.o(.text)
    RCC_AHB1PeriphResetCmd                   0x08009a93   Thumb Code    34  stm32f4xx_rcc.o(.text)
    RCC_AHB2PeriphResetCmd                   0x08009ab5   Thumb Code    34  stm32f4xx_rcc.o(.text)
    RCC_AHB3PeriphResetCmd                   0x08009ad7   Thumb Code    34  stm32f4xx_rcc.o(.text)
    RCC_APB1PeriphResetCmd                   0x08009af9   Thumb Code    34  stm32f4xx_rcc.o(.text)
    RCC_APB2PeriphResetCmd                   0x08009b1b   Thumb Code    34  stm32f4xx_rcc.o(.text)
    RCC_AHB1PeriphClockLPModeCmd             0x08009b3d   Thumb Code    34  stm32f4xx_rcc.o(.text)
    RCC_AHB2PeriphClockLPModeCmd             0x08009b5f   Thumb Code    34  stm32f4xx_rcc.o(.text)
    RCC_AHB3PeriphClockLPModeCmd             0x08009b81   Thumb Code    34  stm32f4xx_rcc.o(.text)
    RCC_APB1PeriphClockLPModeCmd             0x08009ba3   Thumb Code    34  stm32f4xx_rcc.o(.text)
    RCC_APB2PeriphClockLPModeCmd             0x08009bc5   Thumb Code    34  stm32f4xx_rcc.o(.text)
    RCC_LSEModeConfig                        0x08009be7   Thumb Code    40  stm32f4xx_rcc.o(.text)
    RCC_ITConfig                             0x08009c0f   Thumb Code    34  stm32f4xx_rcc.o(.text)
    RCC_ClearFlag                            0x08009c31   Thumb Code    18  stm32f4xx_rcc.o(.text)
    RCC_GetITStatus                          0x08009c43   Thumb Code    22  stm32f4xx_rcc.o(.text)
    RCC_ClearITPendingBit                    0x08009c59   Thumb Code     8  stm32f4xx_rcc.o(.text)
    USART_DeInit                             0x08009c65   Thumb Code   206  stm32f4xx_usart.o(.text)
    USART_Init                               0x08009d33   Thumb Code   204  stm32f4xx_usart.o(.text)
    USART_StructInit                         0x08009dff   Thumb Code    24  stm32f4xx_usart.o(.text)
    USART_ClockInit                          0x08009e17   Thumb Code    32  stm32f4xx_usart.o(.text)
    USART_ClockStructInit                    0x08009e37   Thumb Code    12  stm32f4xx_usart.o(.text)
    USART_Cmd                                0x08009e43   Thumb Code    24  stm32f4xx_usart.o(.text)
    USART_SetPrescaler                       0x08009e5b   Thumb Code    16  stm32f4xx_usart.o(.text)
    USART_OverSampling8Cmd                   0x08009e6b   Thumb Code    22  stm32f4xx_usart.o(.text)
    USART_OneBitMethodCmd                    0x08009e81   Thumb Code    24  stm32f4xx_usart.o(.text)
    USART_SendData                           0x08009e99   Thumb Code     8  stm32f4xx_usart.o(.text)
    USART_ReceiveData                        0x08009ea1   Thumb Code    10  stm32f4xx_usart.o(.text)
    USART_SetAddress                         0x08009eab   Thumb Code    18  stm32f4xx_usart.o(.text)
    USART_ReceiverWakeUpCmd                  0x08009ebd   Thumb Code    24  stm32f4xx_usart.o(.text)
    USART_WakeUpConfig                       0x08009ed5   Thumb Code    18  stm32f4xx_usart.o(.text)
    USART_LINBreakDetectLengthConfig         0x08009ee7   Thumb Code    18  stm32f4xx_usart.o(.text)
    USART_LINCmd                             0x08009ef9   Thumb Code    24  stm32f4xx_usart.o(.text)
    USART_SendBreak                          0x08009f11   Thumb Code    10  stm32f4xx_usart.o(.text)
    USART_HalfDuplexCmd                      0x08009f1b   Thumb Code    24  stm32f4xx_usart.o(.text)
    USART_SetGuardTime                       0x08009f33   Thumb Code    16  stm32f4xx_usart.o(.text)
    USART_SmartCardCmd                       0x08009f43   Thumb Code    24  stm32f4xx_usart.o(.text)
    USART_SmartCardNACKCmd                   0x08009f5b   Thumb Code    24  stm32f4xx_usart.o(.text)
    USART_IrDAConfig                         0x08009f73   Thumb Code    18  stm32f4xx_usart.o(.text)
    USART_IrDACmd                            0x08009f85   Thumb Code    24  stm32f4xx_usart.o(.text)
    USART_DMACmd                             0x08009f9d   Thumb Code    18  stm32f4xx_usart.o(.text)
    USART_ITConfig                           0x08009faf   Thumb Code    74  stm32f4xx_usart.o(.text)
    USART_GetFlagStatus                      0x08009ff9   Thumb Code    26  stm32f4xx_usart.o(.text)
    USART_ClearFlag                          0x0800a013   Thumb Code    18  stm32f4xx_usart.o(.text)
    USART_GetITStatus                        0x0800a025   Thumb Code   118  stm32f4xx_usart.o(.text)
    USART_ClearITPendingBit                  0x0800a09b   Thumb Code    30  stm32f4xx_usart.o(.text)
    TIM_DeInit                               0x0800a0b9   Thumb Code   346  stm32f4xx_tim.o(.text)
    TIM_TimeBaseInit                         0x0800a213   Thumb Code   104  stm32f4xx_tim.o(.text)
    TIM_TimeBaseStructInit                   0x0800a27b   Thumb Code    18  stm32f4xx_tim.o(.text)
    TIM_PrescalerConfig                      0x0800a28d   Thumb Code     6  stm32f4xx_tim.o(.text)
    TIM_CounterModeConfig                    0x0800a293   Thumb Code    18  stm32f4xx_tim.o(.text)
    TIM_SetCounter                           0x0800a2a5   Thumb Code     4  stm32f4xx_tim.o(.text)
    TIM_SetAutoreload                        0x0800a2a9   Thumb Code     4  stm32f4xx_tim.o(.text)
    TIM_GetCounter                           0x0800a2ad   Thumb Code     6  stm32f4xx_tim.o(.text)
    TIM_GetPrescaler                         0x0800a2b3   Thumb Code     6  stm32f4xx_tim.o(.text)
    TIM_UpdateDisableConfig                  0x0800a2b9   Thumb Code    24  stm32f4xx_tim.o(.text)
    TIM_UpdateRequestConfig                  0x0800a2d1   Thumb Code    24  stm32f4xx_tim.o(.text)
    TIM_ARRPreloadConfig                     0x0800a2e9   Thumb Code    24  stm32f4xx_tim.o(.text)
    TIM_SelectOnePulseMode                   0x0800a301   Thumb Code    18  stm32f4xx_tim.o(.text)
    TIM_SetClockDivision                     0x0800a313   Thumb Code    18  stm32f4xx_tim.o(.text)
    TIM_Cmd                                  0x0800a325   Thumb Code    24  stm32f4xx_tim.o(.text)
    TIM_OC1Init                              0x0800a33d   Thumb Code   114  stm32f4xx_tim.o(.text)
    TIM_OC2Init                              0x0800a3af   Thumb Code   154  stm32f4xx_tim.o(.text)
    TIM_OC3Init                              0x0800a449   Thumb Code   204  stm32f4xx_tim.o(.text)
    TIM_OC4Init                              0x0800a515   Thumb Code   112  stm32f4xx_tim.o(.text)
    TIM_OCStructInit                         0x0800a585   Thumb Code    20  stm32f4xx_tim.o(.text)
    TIM_SelectOCxM                           0x0800a599   Thumb Code    86  stm32f4xx_tim.o(.text)
    TIM_SetCompare1                          0x0800a5ef   Thumb Code     4  stm32f4xx_tim.o(.text)
    TIM_SetCompare2                          0x0800a5f3   Thumb Code     4  stm32f4xx_tim.o(.text)
    TIM_SetCompare3                          0x0800a5f7   Thumb Code     4  stm32f4xx_tim.o(.text)
    TIM_SetCompare4                          0x0800a5fb   Thumb Code     4  stm32f4xx_tim.o(.text)
    TIM_ForcedOC1Config                      0x0800a5ff   Thumb Code    18  stm32f4xx_tim.o(.text)
    TIM_ForcedOC2Config                      0x0800a611   Thumb Code    26  stm32f4xx_tim.o(.text)
    TIM_ForcedOC3Config                      0x0800a62b   Thumb Code    18  stm32f4xx_tim.o(.text)
    TIM_ForcedOC4Config                      0x0800a63d   Thumb Code    26  stm32f4xx_tim.o(.text)
    TIM_OC1PreloadConfig                     0x0800a657   Thumb Code    18  stm32f4xx_tim.o(.text)
    TIM_OC2PreloadConfig                     0x0800a669   Thumb Code    26  stm32f4xx_tim.o(.text)
    TIM_OC3PreloadConfig                     0x0800a683   Thumb Code    18  stm32f4xx_tim.o(.text)
    TIM_OC4PreloadConfig                     0x0800a695   Thumb Code    26  stm32f4xx_tim.o(.text)
    TIM_OC1FastConfig                        0x0800a6af   Thumb Code    18  stm32f4xx_tim.o(.text)
    TIM_OC2FastConfig                        0x0800a6c1   Thumb Code    26  stm32f4xx_tim.o(.text)
    TIM_OC3FastConfig                        0x0800a6db   Thumb Code    18  stm32f4xx_tim.o(.text)
    TIM_OC4FastConfig                        0x0800a6ed   Thumb Code    26  stm32f4xx_tim.o(.text)
    TIM_ClearOC1Ref                          0x0800a707   Thumb Code    18  stm32f4xx_tim.o(.text)
    TIM_ClearOC2Ref                          0x0800a719   Thumb Code    24  stm32f4xx_tim.o(.text)
    TIM_ClearOC3Ref                          0x0800a731   Thumb Code    18  stm32f4xx_tim.o(.text)
    TIM_ClearOC4Ref                          0x0800a743   Thumb Code    24  stm32f4xx_tim.o(.text)
    TIM_OC1PolarityConfig                    0x0800a75b   Thumb Code    18  stm32f4xx_tim.o(.text)
    TIM_OC1NPolarityConfig                   0x0800a76d   Thumb Code    18  stm32f4xx_tim.o(.text)
    TIM_OC2PolarityConfig                    0x0800a77f   Thumb Code    26  stm32f4xx_tim.o(.text)
    TIM_OC2NPolarityConfig                   0x0800a799   Thumb Code    26  stm32f4xx_tim.o(.text)
    TIM_OC3PolarityConfig                    0x0800a7b3   Thumb Code    26  stm32f4xx_tim.o(.text)
    TIM_OC3NPolarityConfig                   0x0800a7cd   Thumb Code    26  stm32f4xx_tim.o(.text)
    TIM_OC4PolarityConfig                    0x0800a7e7   Thumb Code    26  stm32f4xx_tim.o(.text)
    TIM_CCxCmd                               0x0800a801   Thumb Code    30  stm32f4xx_tim.o(.text)
    TIM_CCxNCmd                              0x0800a81f   Thumb Code    30  stm32f4xx_tim.o(.text)
    TIM_SetIC4Prescaler                      0x0800a83d   Thumb Code    26  stm32f4xx_tim.o(.text)
    TIM_SetIC3Prescaler                      0x0800a8a7   Thumb Code    18  stm32f4xx_tim.o(.text)
    TIM_SetIC2Prescaler                      0x0800a901   Thumb Code    26  stm32f4xx_tim.o(.text)
    TIM_SetIC1Prescaler                      0x0800a975   Thumb Code    18  stm32f4xx_tim.o(.text)
    TIM_ICInit                               0x0800a9c1   Thumb Code   110  stm32f4xx_tim.o(.text)
    TIM_ICStructInit                         0x0800aa2f   Thumb Code    18  stm32f4xx_tim.o(.text)
    TIM_PWMIConfig                           0x0800aa41   Thumb Code   124  stm32f4xx_tim.o(.text)
    TIM_GetCapture1                          0x0800aabd   Thumb Code     6  stm32f4xx_tim.o(.text)
    TIM_GetCapture2                          0x0800aac3   Thumb Code     6  stm32f4xx_tim.o(.text)
    TIM_GetCapture3                          0x0800aac9   Thumb Code     6  stm32f4xx_tim.o(.text)
    TIM_GetCapture4                          0x0800aacf   Thumb Code     6  stm32f4xx_tim.o(.text)
    TIM_BDTRConfig                           0x0800aad5   Thumb Code    32  stm32f4xx_tim.o(.text)
    TIM_BDTRStructInit                       0x0800aaf5   Thumb Code    18  stm32f4xx_tim.o(.text)
    TIM_CtrlPWMOutputs                       0x0800ab07   Thumb Code    30  stm32f4xx_tim.o(.text)
    TIM_SelectCOM                            0x0800ab25   Thumb Code    24  stm32f4xx_tim.o(.text)
    TIM_CCPreloadControl                     0x0800ab3d   Thumb Code    24  stm32f4xx_tim.o(.text)
    TIM_ITConfig                             0x0800ab55   Thumb Code    18  stm32f4xx_tim.o(.text)
    TIM_GenerateEvent                        0x0800ab67   Thumb Code     4  stm32f4xx_tim.o(.text)
    TIM_GetFlagStatus                        0x0800ab6b   Thumb Code    18  stm32f4xx_tim.o(.text)
    TIM_ClearFlag                            0x0800ab7d   Thumb Code     6  stm32f4xx_tim.o(.text)
    TIM_GetITStatus                          0x0800ab83   Thumb Code    34  stm32f4xx_tim.o(.text)
    TIM_ClearITPendingBit                    0x0800aba5   Thumb Code     6  stm32f4xx_tim.o(.text)
    TIM_DMAConfig                            0x0800abab   Thumb Code    10  stm32f4xx_tim.o(.text)
    TIM_DMACmd                               0x0800abb5   Thumb Code    18  stm32f4xx_tim.o(.text)
    TIM_SelectCCDMA                          0x0800abc7   Thumb Code    24  stm32f4xx_tim.o(.text)
    TIM_InternalClockConfig                  0x0800abdf   Thumb Code    12  stm32f4xx_tim.o(.text)
    TIM_SelectInputTrigger                   0x0800abeb   Thumb Code    18  stm32f4xx_tim.o(.text)
    TIM_ITRxExternalClockConfig              0x0800abfd   Thumb Code    24  stm32f4xx_tim.o(.text)
    TIM_TIxExternalClockConfig               0x0800ac15   Thumb Code    62  stm32f4xx_tim.o(.text)
    TIM_ETRConfig                            0x0800ac53   Thumb Code    28  stm32f4xx_tim.o(.text)
    TIM_ETRClockMode1Config                  0x0800ac6f   Thumb Code    54  stm32f4xx_tim.o(.text)
    TIM_ETRClockMode2Config                  0x0800aca5   Thumb Code    32  stm32f4xx_tim.o(.text)
    TIM_SelectOutputTrigger                  0x0800acc5   Thumb Code    18  stm32f4xx_tim.o(.text)
    TIM_SelectSlaveMode                      0x0800acd7   Thumb Code    18  stm32f4xx_tim.o(.text)
    TIM_SelectMasterSlaveMode                0x0800ace9   Thumb Code    18  stm32f4xx_tim.o(.text)
    TIM_EncoderInterfaceConfig               0x0800acfb   Thumb Code    66  stm32f4xx_tim.o(.text)
    TIM_SelectHallSensor                     0x0800ad3d   Thumb Code    24  stm32f4xx_tim.o(.text)
    TIM_RemapConfig                          0x0800ad55   Thumb Code     6  stm32f4xx_tim.o(.text)
    ADC_DeInit                               0x0800ad5d   Thumb Code    22  stm32f4xx_adc.o(.text)
    ADC_Init                                 0x0800ad73   Thumb Code    74  stm32f4xx_adc.o(.text)
    ADC_StructInit                           0x0800adbd   Thumb Code    20  stm32f4xx_adc.o(.text)
    ADC_CommonInit                           0x0800add1   Thumb Code    34  stm32f4xx_adc.o(.text)
    ADC_CommonStructInit                     0x0800adf3   Thumb Code    12  stm32f4xx_adc.o(.text)
    ADC_Cmd                                  0x0800adff   Thumb Code    22  stm32f4xx_adc.o(.text)
    ADC_AnalogWatchdogCmd                    0x0800ae15   Thumb Code    16  stm32f4xx_adc.o(.text)
    ADC_AnalogWatchdogThresholdsConfig       0x0800ae25   Thumb Code     6  stm32f4xx_adc.o(.text)
    ADC_AnalogWatchdogSingleChannelConfig    0x0800ae2b   Thumb Code    16  stm32f4xx_adc.o(.text)
    ADC_TempSensorVrefintCmd                 0x0800ae3b   Thumb Code    34  stm32f4xx_adc.o(.text)
    ADC_VBATCmd                              0x0800ae5d   Thumb Code    34  stm32f4xx_adc.o(.text)
    ADC_RegularChannelConfig                 0x0800ae7f   Thumb Code   184  stm32f4xx_adc.o(.text)
    ADC_SoftwareStartConv                    0x0800af37   Thumb Code    10  stm32f4xx_adc.o(.text)
    ADC_GetSoftwareStartConvStatus           0x0800af41   Thumb Code    20  stm32f4xx_adc.o(.text)
    ADC_EOCOnEachRegularChannelCmd           0x0800af55   Thumb Code    22  stm32f4xx_adc.o(.text)
    ADC_ContinuousModeCmd                    0x0800af6b   Thumb Code    22  stm32f4xx_adc.o(.text)
    ADC_DiscModeChannelCountConfig           0x0800af81   Thumb Code    24  stm32f4xx_adc.o(.text)
    ADC_DiscModeCmd                          0x0800af99   Thumb Code    22  stm32f4xx_adc.o(.text)
    ADC_GetConversionValue                   0x0800afaf   Thumb Code     8  stm32f4xx_adc.o(.text)
    ADC_GetMultiModeConversionValue          0x0800afb7   Thumb Code     8  stm32f4xx_adc.o(.text)
    ADC_DMACmd                               0x0800afbf   Thumb Code    22  stm32f4xx_adc.o(.text)
    ADC_DMARequestAfterLastTransferCmd       0x0800afd5   Thumb Code    22  stm32f4xx_adc.o(.text)
    ADC_MultiModeDMARequestAfterLastTransferCmd 0x0800afeb   Thumb Code    34  stm32f4xx_adc.o(.text)
    ADC_InjectedChannelConfig                0x0800b00d   Thumb Code   130  stm32f4xx_adc.o(.text)
    ADC_InjectedSequencerLengthConfig        0x0800b08f   Thumb Code    24  stm32f4xx_adc.o(.text)
    ADC_SetInjectedOffset                    0x0800b0a7   Thumb Code    20  stm32f4xx_adc.o(.text)
    ADC_ExternalTrigInjectedConvConfig       0x0800b0bb   Thumb Code    16  stm32f4xx_adc.o(.text)
    ADC_ExternalTrigInjectedConvEdgeConfig   0x0800b0cb   Thumb Code    16  stm32f4xx_adc.o(.text)
    ADC_SoftwareStartInjectedConv            0x0800b0db   Thumb Code    10  stm32f4xx_adc.o(.text)
    ADC_GetSoftwareStartInjectedConvCmdStatus 0x0800b0e5   Thumb Code    20  stm32f4xx_adc.o(.text)
    ADC_AutoInjectedConvCmd                  0x0800b0f9   Thumb Code    22  stm32f4xx_adc.o(.text)
    ADC_InjectedDiscModeCmd                  0x0800b10f   Thumb Code    22  stm32f4xx_adc.o(.text)
    ADC_GetInjectedConversionValue           0x0800b125   Thumb Code    28  stm32f4xx_adc.o(.text)
    ADC_ITConfig                             0x0800b141   Thumb Code    56  stm32f4xx_adc.o(.text)
    ADC_GetFlagStatus                        0x0800b179   Thumb Code    18  stm32f4xx_adc.o(.text)
    ADC_ClearFlag                            0x0800b18b   Thumb Code     6  stm32f4xx_adc.o(.text)
    ADC_GetITStatus                          0x0800b191   Thumb Code    38  stm32f4xx_adc.o(.text)
    ADC_ClearITPendingBit                    0x0800b1b7   Thumb Code    10  stm32f4xx_adc.o(.text)
    DMA_DeInit                               0x0800b1c1   Thumb Code   324  stm32f4xx_dma.o(.text)
    DMA_Init                                 0x0800b305   Thumb Code    82  stm32f4xx_dma.o(.text)
    DMA_StructInit                           0x0800b357   Thumb Code    34  stm32f4xx_dma.o(.text)
    DMA_Cmd                                  0x0800b379   Thumb Code    22  stm32f4xx_dma.o(.text)
    DMA_PeriphIncOffsetSizeConfig            0x0800b38f   Thumb Code    22  stm32f4xx_dma.o(.text)
    DMA_FlowControllerConfig                 0x0800b3a5   Thumb Code    22  stm32f4xx_dma.o(.text)
    DMA_SetCurrDataCounter                   0x0800b3bb   Thumb Code     4  stm32f4xx_dma.o(.text)
    DMA_GetCurrDataCounter                   0x0800b3bf   Thumb Code     8  stm32f4xx_dma.o(.text)
    DMA_DoubleBufferModeConfig               0x0800b3c7   Thumb Code    24  stm32f4xx_dma.o(.text)
    DMA_DoubleBufferModeCmd                  0x0800b3df   Thumb Code    22  stm32f4xx_dma.o(.text)
    DMA_MemoryTargetConfig                   0x0800b3f5   Thumb Code    10  stm32f4xx_dma.o(.text)
    DMA_GetCurrentMemoryTarget               0x0800b3ff   Thumb Code    20  stm32f4xx_dma.o(.text)
    DMA_GetCmdStatus                         0x0800b413   Thumb Code    20  stm32f4xx_dma.o(.text)
    DMA_GetFIFOStatus                        0x0800b427   Thumb Code    12  stm32f4xx_dma.o(.text)
    DMA_GetFlagStatus                        0x0800b433   Thumb Code    56  stm32f4xx_dma.o(.text)
    DMA_ClearFlag                            0x0800b46b   Thumb Code    40  stm32f4xx_dma.o(.text)
    DMA_ITConfig                             0x0800b493   Thumb Code    58  stm32f4xx_dma.o(.text)
    DMA_GetITStatus                          0x0800b4cd   Thumb Code    84  stm32f4xx_dma.o(.text)
    DMA_ClearITPendingBit                    0x0800b521   Thumb Code    40  stm32f4xx_dma.o(.text)
    arm_radix4_butterfly_f32                 0x0800b569   Thumb Code   800  arm_cfft_radix4_f32.o(.text)
    arm_radix4_butterfly_inverse_f32         0x0800b889   Thumb Code   836  arm_cfft_radix4_f32.o(.text)
    arm_cfft_radix4_f32                      0x0800bbcd   Thumb Code    60  arm_cfft_radix4_f32.o(.text)
    arm_cfft_radix4_init_f32                 0x0800bc09   Thumb Code   146  arm_cfft_radix4_init_f32.o(.text)
    arm_bitreversal_f32                      0x0800bcc9   Thumb Code   178  arm_bitreversal.o(.text)
    arm_bitreversal_q31                      0x0800bd7b   Thumb Code   180  arm_bitreversal.o(.text)
    arm_bitreversal_q15                      0x0800be2f   Thumb Code   128  arm_bitreversal.o(.text)
    __use_no_semihosting                     0x0800beaf   Thumb Code     2  use_no_semi_2.o(.text)
    __2printf                                0x0800beb1   Thumb Code    20  noretval__2printf.o(.text)
    __2sprintf                               0x0800bec9   Thumb Code    34  noretval__2sprintf.o(.text)
    _printf_str                              0x0800bef1   Thumb Code    82  _printf_str.o(.text)
    _printf_int_dec                          0x0800bf45   Thumb Code   104  _printf_dec.o(.text)
    _printf_int_hex                          0x0800bfbd   Thumb Code    84  _printf_hex_int.o(.text)
    _printf_longlong_hex                     0x0800bfbd   Thumb Code     0  _printf_hex_int.o(.text)
    __printf                                 0x0800c015   Thumb Code   270  __printf_wp.o(.text)
    strcat                                   0x0800c123   Thumb Code    24  strcat.o(.text)
    __aeabi_memclr                           0x0800c13b   Thumb Code     0  rt_memclr.o(.text)
    __rt_memclr                              0x0800c13b   Thumb Code    68  rt_memclr.o(.text)
    _memset                                  0x0800c13f   Thumb Code     0  rt_memclr.o(.text)
    __aeabi_memclr4                          0x0800c17f   Thumb Code     0  rt_memclr_w.o(.text)
    __aeabi_memclr8                          0x0800c17f   Thumb Code     0  rt_memclr_w.o(.text)
    __rt_memclr_w                            0x0800c17f   Thumb Code    78  rt_memclr_w.o(.text)
    _memset_w                                0x0800c183   Thumb Code     0  rt_memclr_w.o(.text)
    strcmp                                   0x0800c1cd   Thumb Code   128  strcmpv7m.o(.text)
    __use_two_region_memory                  0x0800c24d   Thumb Code     2  heapauxi.o(.text)
    __rt_heap_escrow$2region                 0x0800c24f   Thumb Code     2  heapauxi.o(.text)
    __rt_heap_expand$2region                 0x0800c251   Thumb Code     2  heapauxi.o(.text)
    __I$use$semihosting                      0x0800c253   Thumb Code     0  use_no_semi.o(.text)
    __use_no_semihosting_swi                 0x0800c253   Thumb Code     2  use_no_semi.o(.text)
    __read_errno                             0x0800c255   Thumb Code    10  _rserrno.o(.text)
    __set_errno                              0x0800c25f   Thumb Code    12  _rserrno.o(.text)
    _printf_int_common                       0x0800c26b   Thumb Code   178  _printf_intcommon.o(.text)
    __lib_sel_fp_printf                      0x0800c31d   Thumb Code     2  _printf_fp_dec.o(.text)
    _printf_fp_dec_real                      0x0800c4cf   Thumb Code   620  _printf_fp_dec.o(.text)
    _printf_char_common                      0x0800c747   Thumb Code    32  _printf_char_common.o(.text)
    _sputc                                   0x0800c76d   Thumb Code    10  _sputc.o(.text)
    _printf_cs_common                        0x0800c777   Thumb Code    20  _printf_char.o(.text)
    _printf_char                             0x0800c78b   Thumb Code    16  _printf_char.o(.text)
    _printf_string                           0x0800c79b   Thumb Code     8  _printf_char.o(.text)
    _printf_char_file                        0x0800c7a5   Thumb Code    32  _printf_char_file.o(.text)
    __rt_locale                              0x0800c7c9   Thumb Code     8  rt_locale_intlibspace.o(.text)
    __aeabi_errno_addr                       0x0800c7d1   Thumb Code     8  rt_errno_addr_intlibspace.o(.text)
    __errno$intlibspace                      0x0800c7d1   Thumb Code     0  rt_errno_addr_intlibspace.o(.text)
    __rt_errno_addr$intlibspace              0x0800c7d1   Thumb Code     0  rt_errno_addr_intlibspace.o(.text)
    _ll_udiv10                               0x0800c7d9   Thumb Code   138  lludiv10.o(.text)
    _printf_fp_infnan                        0x0800c865   Thumb Code   112  _printf_fp_infnan.o(.text)
    _btod_etento                             0x0800c8e5   Thumb Code   224  bigflt0.o(.text)
    ferror                                   0x0800c9c9   Thumb Code     8  ferror.o(.text)
    __user_libspace                          0x0800c9d1   Thumb Code     8  libspace.o(.text)
    __user_perproc_libspace                  0x0800c9d1   Thumb Code     0  libspace.o(.text)
    __user_perthread_libspace                0x0800c9d1   Thumb Code     0  libspace.o(.text)
    __user_setup_stackheap                   0x0800c9d9   Thumb Code    74  sys_stackheap_outer.o(.text)
    exit                                     0x0800ca23   Thumb Code    18  exit.o(.text)
    _btod_d2e                                0x0800ca35   Thumb Code    62  btod.o(CL$$btod_d2e)
    _d2e_denorm_low                          0x0800ca73   Thumb Code    70  btod.o(CL$$btod_d2e_denorm_low)
    _d2e_norm_op1                            0x0800cab9   Thumb Code    96  btod.o(CL$$btod_d2e_norm_op1)
    __btod_div_common                        0x0800cb19   Thumb Code   696  btod.o(CL$$btod_div_common)
    _e2e                                     0x0800ce51   Thumb Code   220  btod.o(CL$$btod_e2e)
    _btod_ediv                               0x0800cf2d   Thumb Code    42  btod.o(CL$$btod_ediv)
    _btod_emul                               0x0800cf57   Thumb Code    42  btod.o(CL$$btod_emul)
    __btod_mult_common                       0x0800cf81   Thumb Code   580  btod.o(CL$$btod_mult_common)
    __ARM_fpclassify                         0x0800d1c5   Thumb Code    48  fpclassify.o(i.__ARM_fpclassify)
    __hardfp_sqrt                            0x0800d1f5   Thumb Code   122  sqrt.o(i.__hardfp_sqrt)
    _is_digit                                0x0800d26f   Thumb Code    14  __printf_wp.o(i._is_digit)
    _get_lc_numeric                          0x0800d27d   Thumb Code    44  lc_numeric_c.o(locale$$code)
    __aeabi_dadd                             0x0800d2a9   Thumb Code     0  daddsub_clz.o(x$fpl$dadd)
    _dadd                                    0x0800d2a9   Thumb Code   332  daddsub_clz.o(x$fpl$dadd)
    __fpl_dcmp_Inf                           0x0800d3f9   Thumb Code    24  dcmpi.o(x$fpl$dcmpinf)
    __aeabi_ddiv                             0x0800d411   Thumb Code     0  ddiv.o(x$fpl$ddiv)
    _ddiv                                    0x0800d411   Thumb Code   552  ddiv.o(x$fpl$ddiv)
    __aeabi_d2iz                             0x0800d6c1   Thumb Code     0  dfix.o(x$fpl$dfix)
    _dfix                                    0x0800d6c1   Thumb Code    94  dfix.o(x$fpl$dfix)
    __aeabi_d2uiz                            0x0800d721   Thumb Code     0  dfixu.o(x$fpl$dfixu)
    _dfixu                                   0x0800d721   Thumb Code    90  dfixu.o(x$fpl$dfixu)
    __aeabi_i2d                              0x0800d77b   Thumb Code     0  dflt_clz.o(x$fpl$dflt)
    _dflt                                    0x0800d77b   Thumb Code    46  dflt_clz.o(x$fpl$dflt)
    __aeabi_ui2d                             0x0800d7a9   Thumb Code     0  dflt_clz.o(x$fpl$dfltu)
    _dfltu                                   0x0800d7a9   Thumb Code    38  dflt_clz.o(x$fpl$dfltu)
    __aeabi_cdcmple                          0x0800d7d1   Thumb Code     0  dleqf.o(x$fpl$dleqf)
    _dcmple                                  0x0800d7d1   Thumb Code   120  dleqf.o(x$fpl$dleqf)
    __fpl_dcmple_InfNaN                      0x0800d833   Thumb Code     0  dleqf.o(x$fpl$dleqf)
    __aeabi_dmul                             0x0800d849   Thumb Code     0  dmul.o(x$fpl$dmul)
    _dmul                                    0x0800d849   Thumb Code   332  dmul.o(x$fpl$dmul)
    __fpl_dnaninf                            0x0800d99d   Thumb Code   156  dnaninf.o(x$fpl$dnaninf)
    __fpl_dretinf                            0x0800da39   Thumb Code    12  dretinf.o(x$fpl$dretinf)
    __aeabi_cdrcmple                         0x0800da45   Thumb Code     0  drleqf.o(x$fpl$drleqf)
    _drcmple                                 0x0800da45   Thumb Code   108  drleqf.o(x$fpl$drleqf)
    __aeabi_drsub                            0x0800dab1   Thumb Code     0  daddsub_clz.o(x$fpl$drsb)
    _drsb                                    0x0800dab1   Thumb Code    22  daddsub_clz.o(x$fpl$drsb)
    _dsqrt                                   0x0800dac9   Thumb Code   404  dsqrt_umaal.o(x$fpl$dsqrt)
    __aeabi_dsub                             0x0800dc61   Thumb Code     0  daddsub_clz.o(x$fpl$dsub)
    _dsub                                    0x0800dc61   Thumb Code   464  daddsub_clz.o(x$fpl$dsub)
    __aeabi_f2d                              0x0800de35   Thumb Code     0  f2d.o(x$fpl$f2d)
    _f2d                                     0x0800de35   Thumb Code    86  f2d.o(x$fpl$f2d)
    __fpl_fnaninf                            0x0800de8b   Thumb Code   140  fnaninf.o(x$fpl$fnaninf)
    _fp_init                                 0x0800df17   Thumb Code    10  fpinit.o(x$fpl$fpinit)
    __fplib_config_fpu_vfp                   0x0800df1f   Thumb Code     0  fpinit.o(x$fpl$fpinit)
    __fplib_config_pureend_doubles           0x0800df1f   Thumb Code     0  fpinit.o(x$fpl$fpinit)
    _printf_fp_dec                           0x0800df21   Thumb Code     4  printf1.o(x$fpl$printf1)
    __I$use$fp                               0x0800df24   Number         0  usenofp.o(x$fpl$usenofp)
    asc2_1206                                0x0800df24   Data        1140  lcd.o(.constdata)
    asc2_1608                                0x0800e398   Data        1520  lcd.o(.constdata)
    asc2_2412                                0x0800e988   Data        3420  lcd.o(.constdata)
    chinese                                  0x0800f6e4   Data         108  lcd.o(.constdata)
    FT5206_TPX_TBL                           0x0800f750   Data          10  ft5206.o(.constdata)
    GT911_TPX_TBL                            0x0800f75a   Data          10  ft5206.o(.constdata)
    GT9147_CFG_TBL                           0x0800f764   Data         184  gt9147.o(.constdata)
    GT9147_TPX_TBL                           0x0800f81c   Data          10  gt9147.o(.constdata)
    OTT_TPX_TBL                              0x0800f826   Data          10  ott2001a.o(.constdata)
    TP_REMIND_MSG_TBL                        0x0800f830   Data           4  touch.o(.constdata)
    pga_cal_table                            0x0800f848   Data        1024  search.o(.constdata)
    required_vin_for_1V_table                0x0800fc48   Data         120  search.o(.constdata)
    FSMC_DefaultTimingStruct                 0x0800fcc0   Data          28  stm32f4xx_fsmc.o(.constdata)
    armBitRevTable                           0x0800fcdc   Data        2048  arm_common_tables.o(.constdata)
    twiddleCoef_4096                         0x080104dc   Data       32768  arm_common_tables.o(.constdata)
    Region$$Table$$Base                      0x08018614   Number         0  anon$$obj.o(Region$$Table)
    Region$$Table$$Limit                     0x08018634   Number         0  anon$$obj.o(Region$$Table)
    Window_Num                               0x20000000   Data           4  main.o(.data)
    g_adc_capture_done                       0x20000004   Data           1  main.o(.data)
    SystemCoreClock                          0x2000000c   Data           4  system_stm32f4xx.o(.data)
    AHBPrescTable                            0x20000010   Data          16  system_stm32f4xx.o(.data)
    POINT_COLOR                              0x20000022   Data           2  lcd.o(.data)
    BACK_COLOR                               0x20000024   Data           2  lcd.o(.data)
    CIP                                      0x20000026   Data           5  ft5206.o(.data)
    g_gt_tnum                                0x2000002b   Data           1  ft5206.o(.data)
    tp_dev                                   0x20000030   Data          52  touch.o(.data)
    CMD_RDX                                  0x20000064   Data           1  touch.o(.data)
    CMD_RDY                                  0x20000065   Data           1  touch.o(.data)
    __stdout                                 0x200000bc   Data           4  usart.o(.data)
    USART_RX_STA                             0x200000c0   Data           2  usart.o(.data)
    Window                                   0x200000d4   Data       16384  main.o(.bss)
    adc_dual_buf                             0x200040d4   Data       32768  main.o(.bss)
    vin_buf                                  0x2000c0d4   Data       16384  main.o(.bss)
    vout_buf                                 0x200100d4   Data       16384  main.o(.bss)
    gain_results                             0x200140d4   Data        4984  main.o(.bss)
    filtered_gain_results                    0x2001544c   Data        4984  main.o(.bss)
    lcddev                                   0x200167c4   Data          14  lcd.o(.bss)
    USART_RX_BUF                             0x2001e854   Data         200  usart.o(.bss)
    __libspace_start                         0x2001e91c   Data          96  libspace.o(.bss)
    __temporary_stack_top$libspace           0x2001e97c   Data           0  libspace.o(.bss)



==============================================================================

Memory Map of the image

  Image Entry point : 0x08000189

  Load Region LR_IROM1 (Base: 0x08000000, Size: 0x00018724, Max: 0x00100000, ABSOLUTE)

    Execution Region ER_IROM1 (Exec base: 0x08000000, Load base: 0x08000000, Size: 0x00018650, Max: 0x00100000, ABSOLUTE)

    Exec Addr    Load Addr    Size         Type   Attr      Idx    E Section Name        Object

    0x08000000   0x08000000   0x00000188   Data   RO          844    RESET               startup_stm32f40_41xxx.o
    0x08000188   0x08000188   0x00000008   Code   RO         1268  * !!!main             c_w.l(__main.o)
    0x08000190   0x08000190   0x00000034   Code   RO         1600    !!!scatter          c_w.l(__scatter.o)
    0x080001c4   0x080001c4   0x0000001a   Code   RO         1602    !!handler_copy      c_w.l(__scatter_copy.o)
    0x080001de   0x080001de   0x00000002   PAD
    0x080001e0   0x080001e0   0x0000001c   Code   RO         1604    !!handler_zi        c_w.l(__scatter_zi.o)
    0x080001fc   0x080001fc   0x00000000   Code   RO         1257    .ARM.Collect$$_printf_percent$$00000000  c_w.l(_printf_percent.o)
    0x080001fc   0x080001fc   0x00000006   Code   RO         1256    .ARM.Collect$$_printf_percent$$00000003  c_w.l(_printf_f.o)
    0x08000202   0x08000202   0x00000006   Code   RO         1255    .ARM.Collect$$_printf_percent$$00000009  c_w.l(_printf_d.o)
    0x08000208   0x08000208   0x00000006   Code   RO         1254    .ARM.Collect$$_printf_percent$$0000000C  c_w.l(_printf_x.o)
    0x0800020e   0x0800020e   0x00000006   Code   RO         1253    .ARM.Collect$$_printf_percent$$00000014  c_w.l(_printf_s.o)
    0x08000214   0x08000214   0x00000004   Code   RO         1347    .ARM.Collect$$_printf_percent$$00000017  c_w.l(_printf_percent_end.o)
    0x08000218   0x08000218   0x00000002   Code   RO         1475    .ARM.Collect$$libinit$$00000000  c_w.l(libinit.o)
    0x0800021a   0x0800021a   0x00000004   Code   RO         1476    .ARM.Collect$$libinit$$00000001  c_w.l(libinit2.o)
    0x0800021e   0x0800021e   0x00000000   Code   RO         1479    .ARM.Collect$$libinit$$00000004  c_w.l(libinit2.o)
    0x0800021e   0x0800021e   0x00000000   Code   RO         1482    .ARM.Collect$$libinit$$0000000A  c_w.l(libinit2.o)
    0x0800021e   0x0800021e   0x00000000   Code   RO         1484    .ARM.Collect$$libinit$$0000000C  c_w.l(libinit2.o)
    0x0800021e   0x0800021e   0x00000000   Code   RO         1486    .ARM.Collect$$libinit$$0000000E  c_w.l(libinit2.o)
    0x0800021e   0x0800021e   0x00000006   Code   RO         1487    .ARM.Collect$$libinit$$0000000F  c_w.l(libinit2.o)
    0x08000224   0x08000224   0x00000000   Code   RO         1489    .ARM.Collect$$libinit$$00000011  c_w.l(libinit2.o)
    0x08000224   0x08000224   0x00000000   Code   RO         1491    .ARM.Collect$$libinit$$00000013  c_w.l(libinit2.o)
    0x08000224   0x08000224   0x00000000   Code   RO         1493    .ARM.Collect$$libinit$$00000015  c_w.l(libinit2.o)
    0x08000224   0x08000224   0x0000000a   Code   RO         1494    .ARM.Collect$$libinit$$00000016  c_w.l(libinit2.o)
    0x0800022e   0x0800022e   0x00000000   Code   RO         1495    .ARM.Collect$$libinit$$00000017  c_w.l(libinit2.o)
    0x0800022e   0x0800022e   0x00000000   Code   RO         1497    .ARM.Collect$$libinit$$00000019  c_w.l(libinit2.o)
    0x0800022e   0x0800022e   0x00000000   Code   RO         1499    .ARM.Collect$$libinit$$0000001B  c_w.l(libinit2.o)
    0x0800022e   0x0800022e   0x00000000   Code   RO         1501    .ARM.Collect$$libinit$$0000001D  c_w.l(libinit2.o)
    0x0800022e   0x0800022e   0x00000000   Code   RO         1503    .ARM.Collect$$libinit$$0000001F  c_w.l(libinit2.o)
    0x0800022e   0x0800022e   0x00000000   Code   RO         1505    .ARM.Collect$$libinit$$00000021  c_w.l(libinit2.o)
    0x0800022e   0x0800022e   0x00000000   Code   RO         1507    .ARM.Collect$$libinit$$00000023  c_w.l(libinit2.o)
    0x0800022e   0x0800022e   0x00000000   Code   RO         1509    .ARM.Collect$$libinit$$00000025  c_w.l(libinit2.o)
    0x0800022e   0x0800022e   0x00000000   Code   RO         1513    .ARM.Collect$$libinit$$0000002C  c_w.l(libinit2.o)
    0x0800022e   0x0800022e   0x00000000   Code   RO         1515    .ARM.Collect$$libinit$$0000002E  c_w.l(libinit2.o)
    0x0800022e   0x0800022e   0x00000000   Code   RO         1517    .ARM.Collect$$libinit$$00000030  c_w.l(libinit2.o)
    0x0800022e   0x0800022e   0x00000000   Code   RO         1519    .ARM.Collect$$libinit$$00000032  c_w.l(libinit2.o)
    0x0800022e   0x0800022e   0x00000002   Code   RO         1520    .ARM.Collect$$libinit$$00000033  c_w.l(libinit2.o)
    0x08000230   0x08000230   0x00000002   Code   RO         1548    .ARM.Collect$$libshutdown$$00000000  c_w.l(libshutdown.o)
    0x08000232   0x08000232   0x00000000   Code   RO         1557    .ARM.Collect$$libshutdown$$00000002  c_w.l(libshutdown2.o)
    0x08000232   0x08000232   0x00000000   Code   RO         1559    .ARM.Collect$$libshutdown$$00000004  c_w.l(libshutdown2.o)
    0x08000232   0x08000232   0x00000000   Code   RO         1561    .ARM.Collect$$libshutdown$$00000006  c_w.l(libshutdown2.o)
    0x08000232   0x08000232   0x00000000   Code   RO         1564    .ARM.Collect$$libshutdown$$00000009  c_w.l(libshutdown2.o)
    0x08000232   0x08000232   0x00000000   Code   RO         1567    .ARM.Collect$$libshutdown$$0000000C  c_w.l(libshutdown2.o)
    0x08000232   0x08000232   0x00000000   Code   RO         1569    .ARM.Collect$$libshutdown$$0000000E  c_w.l(libshutdown2.o)
    0x08000232   0x08000232   0x00000000   Code   RO         1572    .ARM.Collect$$libshutdown$$00000011  c_w.l(libshutdown2.o)
    0x08000232   0x08000232   0x00000002   Code   RO         1573    .ARM.Collect$$libshutdown$$00000012  c_w.l(libshutdown2.o)
    0x08000234   0x08000234   0x00000000   Code   RO         1332    .ARM.Collect$$rtentry$$00000000  c_w.l(__rtentry.o)
    0x08000234   0x08000234   0x00000000   Code   RO         1387    .ARM.Collect$$rtentry$$00000002  c_w.l(__rtentry2.o)
    0x08000234   0x08000234   0x00000006   Code   RO         1399    .ARM.Collect$$rtentry$$00000004  c_w.l(__rtentry4.o)
    0x0800023a   0x0800023a   0x00000000   Code   RO         1389    .ARM.Collect$$rtentry$$00000009  c_w.l(__rtentry2.o)
    0x0800023a   0x0800023a   0x00000004   Code   RO         1390    .ARM.Collect$$rtentry$$0000000A  c_w.l(__rtentry2.o)
    0x0800023e   0x0800023e   0x00000000   Code   RO         1392    .ARM.Collect$$rtentry$$0000000C  c_w.l(__rtentry2.o)
    0x0800023e   0x0800023e   0x00000008   Code   RO         1393    .ARM.Collect$$rtentry$$0000000D  c_w.l(__rtentry2.o)
    0x08000246   0x08000246   0x00000002   Code   RO         1521    .ARM.Collect$$rtexit$$00000000  c_w.l(rtexit.o)
    0x08000248   0x08000248   0x00000000   Code   RO         1528    .ARM.Collect$$rtexit$$00000002  c_w.l(rtexit2.o)
    0x08000248   0x08000248   0x00000004   Code   RO         1529    .ARM.Collect$$rtexit$$00000003  c_w.l(rtexit2.o)
    0x0800024c   0x0800024c   0x00000006   Code   RO         1530    .ARM.Collect$$rtexit$$00000004  c_w.l(rtexit2.o)
    0x08000252   0x08000252   0x00000002   PAD
    0x08000254   0x08000254   0x000000d8   Code   RO            3    .text               main.o
    0x0800032c   0x0800032c   0x0000015c   Code   RO          218    .text               stm32f4xx_it.o
    0x08000488   0x08000488   0x00000210   Code   RO          269    .text               system_stm32f4xx.o
    0x08000698   0x08000698   0x00000040   Code   RO          295    .text               led.o
    0x080006d8   0x080006d8   0x000000bc   Code   RO          315    .text               key.o
    0x08000794   0x08000794   0x00000070   Code   RO          336    .text               timer.o
    0x08000804   0x08000804   0x00000184   Code   RO          357    .text               adc_dma_timer.o
    0x08000988   0x08000988   0x00003d88   Code   RO          378    .text               lcd.o
    0x08004710   0x08004710   0x000003dc   Code   RO          436    .text               ad9833.o
    0x08004aec   0x08004aec   0x00000098   Code   RO          456    .text               mcp41010.o
    0x08004b84   0x08004b84   0x00000258   Code   RO          476    .text               ctiic.o
    0x08004ddc   0x08004ddc   0x00000594   Code   RO          499    .text               ft5206.o
    0x08005370   0x08005370   0x00000528   Code   RO          523    .text               gt9147.o
    0x08005898   0x08005898   0x00000388   Code   RO          547    .text               ott2001a.o
    0x08005c20   0x08005c20   0x0000116c   Code   RO          571    .text               touch.o
    0x08006d8c   0x08006d8c   0x0000017a   Code   RO          605    .text               24cxx.o
    0x08006f06   0x08006f06   0x00000002   PAD
    0x08006f08   0x08006f08   0x0000025c   Code   RO          625    .text               myiic.o
    0x08007164   0x08007164   0x00000f68   Code   RO          645    .text               ui.o
    0x080080cc   0x080080cc   0x0000003c   Code   RO          675    .text               control.o
    0x08008108   0x08008108   0x0000006c   Code   RO          715    .text               search.o
    0x08008174   0x08008174   0x000008c8   Code   RO          739    .text               dsp_process.o
    0x08008a3c   0x08008a3c   0x00000104   Code   RO          773    .text               delay.o
    0x08008b40   0x08008b40   0x0000014c   Code   RO          813    .text               usart.o
    0x08008c8c   0x08008c8c   0x00000040   Code   RO          845    .text               startup_stm32f40_41xxx.o
    0x08008ccc   0x08008ccc   0x000000e0   Code   RO          851    .text               misc.o
    0x08008dac   0x08008dac   0x00000294   Code   RO          871    .text               stm32f4xx_gpio.o
    0x08009040   0x08009040   0x000005c8   Code   RO          891    .text               stm32f4xx_fsmc.o
    0x08009608   0x08009608   0x0000065c   Code   RO          914    .text               stm32f4xx_rcc.o
    0x08009c64   0x08009c64   0x00000454   Code   RO          956    .text               stm32f4xx_usart.o
    0x0800a0b8   0x0800a0b8   0x00000ca2   Code   RO          976    .text               stm32f4xx_tim.o
    0x0800ad5a   0x0800ad5a   0x00000002   PAD
    0x0800ad5c   0x0800ad5c   0x00000464   Code   RO          996    .text               stm32f4xx_adc.o
    0x0800b1c0   0x0800b1c0   0x000003a8   Code   RO         1016    .text               stm32f4xx_dma.o
    0x0800b568   0x0800b568   0x000006a0   Code   RO         1056    .text               arm_cortexM4lf_math.lib(arm_cfft_radix4_f32.o)
    0x0800bc08   0x0800bc08   0x000000c0   Code   RO         1101    .text               arm_cortexM4lf_math.lib(arm_cfft_radix4_init_f32.o)
    0x0800bcc8   0x0800bcc8   0x000001e6   Code   RO         1125    .text               arm_cortexM4lf_math.lib(arm_bitreversal.o)
    0x0800beae   0x0800beae   0x00000002   Code   RO         1196    .text               c_w.l(use_no_semi_2.o)
    0x0800beb0   0x0800beb0   0x00000018   Code   RO         1202    .text               c_w.l(noretval__2printf.o)
    0x0800bec8   0x0800bec8   0x00000028   Code   RO         1204    .text               c_w.l(noretval__2sprintf.o)
    0x0800bef0   0x0800bef0   0x00000052   Code   RO         1208    .text               c_w.l(_printf_str.o)
    0x0800bf42   0x0800bf42   0x00000002   PAD
    0x0800bf44   0x0800bf44   0x00000078   Code   RO         1210    .text               c_w.l(_printf_dec.o)
    0x0800bfbc   0x0800bfbc   0x00000058   Code   RO         1215    .text               c_w.l(_printf_hex_int.o)
    0x0800c014   0x0800c014   0x0000010e   Code   RO         1241    .text               c_w.l(__printf_wp.o)
    0x0800c122   0x0800c122   0x00000018   Code   RO         1258    .text               c_w.l(strcat.o)
    0x0800c13a   0x0800c13a   0x00000044   Code   RO         1260    .text               c_w.l(rt_memclr.o)
    0x0800c17e   0x0800c17e   0x0000004e   Code   RO         1262    .text               c_w.l(rt_memclr_w.o)
    0x0800c1cc   0x0800c1cc   0x00000080   Code   RO         1264    .text               c_w.l(strcmpv7m.o)
    0x0800c24c   0x0800c24c   0x00000006   Code   RO         1266    .text               c_w.l(heapauxi.o)
    0x0800c252   0x0800c252   0x00000002   Code   RO         1330    .text               c_w.l(use_no_semi.o)
    0x0800c254   0x0800c254   0x00000016   Code   RO         1333    .text               c_w.l(_rserrno.o)
    0x0800c26a   0x0800c26a   0x000000b2   Code   RO         1335    .text               c_w.l(_printf_intcommon.o)
    0x0800c31c   0x0800c31c   0x0000041e   Code   RO         1337    .text               c_w.l(_printf_fp_dec.o)
    0x0800c73a   0x0800c73a   0x00000002   PAD
    0x0800c73c   0x0800c73c   0x00000030   Code   RO         1339    .text               c_w.l(_printf_char_common.o)
    0x0800c76c   0x0800c76c   0x0000000a   Code   RO         1341    .text               c_w.l(_sputc.o)
    0x0800c776   0x0800c776   0x0000002c   Code   RO         1343    .text               c_w.l(_printf_char.o)
    0x0800c7a2   0x0800c7a2   0x00000002   PAD
    0x0800c7a4   0x0800c7a4   0x00000024   Code   RO         1345    .text               c_w.l(_printf_char_file.o)
    0x0800c7c8   0x0800c7c8   0x00000008   Code   RO         1404    .text               c_w.l(rt_locale_intlibspace.o)
    0x0800c7d0   0x0800c7d0   0x00000008   Code   RO         1409    .text               c_w.l(rt_errno_addr_intlibspace.o)
    0x0800c7d8   0x0800c7d8   0x0000008a   Code   RO         1411    .text               c_w.l(lludiv10.o)
    0x0800c862   0x0800c862   0x00000002   PAD
    0x0800c864   0x0800c864   0x00000080   Code   RO         1413    .text               c_w.l(_printf_fp_infnan.o)
    0x0800c8e4   0x0800c8e4   0x000000e4   Code   RO         1417    .text               c_w.l(bigflt0.o)
    0x0800c9c8   0x0800c9c8   0x00000008   Code   RO         1442    .text               c_w.l(ferror.o)
    0x0800c9d0   0x0800c9d0   0x00000008   Code   RO         1463    .text               c_w.l(libspace.o)
    0x0800c9d8   0x0800c9d8   0x0000004a   Code   RO         1466    .text               c_w.l(sys_stackheap_outer.o)
    0x0800ca22   0x0800ca22   0x00000012   Code   RO         1468    .text               c_w.l(exit.o)
    0x0800ca34   0x0800ca34   0x0000003e   Code   RO         1420    CL$$btod_d2e        c_w.l(btod.o)
    0x0800ca72   0x0800ca72   0x00000046   Code   RO         1422    CL$$btod_d2e_denorm_low  c_w.l(btod.o)
    0x0800cab8   0x0800cab8   0x00000060   Code   RO         1421    CL$$btod_d2e_norm_op1  c_w.l(btod.o)
    0x0800cb18   0x0800cb18   0x00000338   Code   RO         1430    CL$$btod_div_common  c_w.l(btod.o)
    0x0800ce50   0x0800ce50   0x000000dc   Code   RO         1427    CL$$btod_e2e        c_w.l(btod.o)
    0x0800cf2c   0x0800cf2c   0x0000002a   Code   RO         1424    CL$$btod_ediv       c_w.l(btod.o)
    0x0800cf56   0x0800cf56   0x0000002a   Code   RO         1423    CL$$btod_emul       c_w.l(btod.o)
    0x0800cf80   0x0800cf80   0x00000244   Code   RO         1429    CL$$btod_mult_common  c_w.l(btod.o)
    0x0800d1c4   0x0800d1c4   0x00000030   Code   RO         1459    i.__ARM_fpclassify  m_wm.l(fpclassify.o)
    0x0800d1f4   0x0800d1f4   0x0000007a   Code   RO         1306    i.__hardfp_sqrt     m_wm.l(sqrt.o)
    0x0800d26e   0x0800d26e   0x0000000e   Code   RO         1243    i._is_digit         c_w.l(__printf_wp.o)
    0x0800d27c   0x0800d27c   0x0000002c   Code   RO         1447    locale$$code        c_w.l(lc_numeric_c.o)
    0x0800d2a8   0x0800d2a8   0x00000150   Code   RO         1272    x$fpl$dadd          fz_wm.l(daddsub_clz.o)
    0x0800d3f8   0x0800d3f8   0x00000018   Code   RO         1348    x$fpl$dcmpinf       fz_wm.l(dcmpi.o)
    0x0800d410   0x0800d410   0x000002b0   Code   RO         1279    x$fpl$ddiv          fz_wm.l(ddiv.o)
    0x0800d6c0   0x0800d6c0   0x0000005e   Code   RO         1282    x$fpl$dfix          fz_wm.l(dfix.o)
    0x0800d71e   0x0800d71e   0x00000002   PAD
    0x0800d720   0x0800d720   0x0000005a   Code   RO         1286    x$fpl$dfixu         fz_wm.l(dfixu.o)
    0x0800d77a   0x0800d77a   0x0000002e   Code   RO         1291    x$fpl$dflt          fz_wm.l(dflt_clz.o)
    0x0800d7a8   0x0800d7a8   0x00000026   Code   RO         1290    x$fpl$dfltu         fz_wm.l(dflt_clz.o)
    0x0800d7ce   0x0800d7ce   0x00000002   PAD
    0x0800d7d0   0x0800d7d0   0x00000078   Code   RO         1296    x$fpl$dleqf         fz_wm.l(dleqf.o)
    0x0800d848   0x0800d848   0x00000154   Code   RO         1298    x$fpl$dmul          fz_wm.l(dmul.o)
    0x0800d99c   0x0800d99c   0x0000009c   Code   RO         1350    x$fpl$dnaninf       fz_wm.l(dnaninf.o)
    0x0800da38   0x0800da38   0x0000000c   Code   RO         1352    x$fpl$dretinf       fz_wm.l(dretinf.o)
    0x0800da44   0x0800da44   0x0000006c   Code   RO         1300    x$fpl$drleqf        fz_wm.l(drleqf.o)
    0x0800dab0   0x0800dab0   0x00000016   Code   RO         1273    x$fpl$drsb          fz_wm.l(daddsub_clz.o)
    0x0800dac6   0x0800dac6   0x00000002   PAD
    0x0800dac8   0x0800dac8   0x00000198   Code   RO         1354    x$fpl$dsqrt         fz_wm.l(dsqrt_umaal.o)
    0x0800dc60   0x0800dc60   0x000001d4   Code   RO         1274    x$fpl$dsub          fz_wm.l(daddsub_clz.o)
    0x0800de34   0x0800de34   0x00000056   Code   RO         1302    x$fpl$f2d           fz_wm.l(f2d.o)
    0x0800de8a   0x0800de8a   0x0000008c   Code   RO         1356    x$fpl$fnaninf       fz_wm.l(fnaninf.o)
    0x0800df16   0x0800df16   0x0000000a   Code   RO         1525    x$fpl$fpinit        fz_wm.l(fpinit.o)
    0x0800df20   0x0800df20   0x00000004   Code   RO         1304    x$fpl$printf1       fz_wm.l(printf1.o)
    0x0800df24   0x0800df24   0x00000000   Code   RO         1360    x$fpl$usenofp       fz_wm.l(usenofp.o)
    0x0800df24   0x0800df24   0x0000182c   Data   RO          380    .constdata          lcd.o
    0x0800f750   0x0800f750   0x00000014   Data   RO          500    .constdata          ft5206.o
    0x0800f764   0x0800f764   0x000000c2   Data   RO          524    .constdata          gt9147.o
    0x0800f826   0x0800f826   0x0000000a   Data   RO          548    .constdata          ott2001a.o
    0x0800f830   0x0800f830   0x00000004   Data   RO          572    .constdata          touch.o
    0x0800f834   0x0800f834   0x00000004   PAD
    0x0800f838   0x0800f838   0x00000010   Data   RO          647    .constdata          ui.o
    0x0800f848   0x0800f848   0x00000478   Data   RO          716    .constdata          search.o
    0x0800fcc0   0x0800fcc0   0x0000001c   Data   RO          892    .constdata          stm32f4xx_fsmc.o
    0x0800fcdc   0x0800fcdc   0x00000800   Data   RO         1145    .constdata          arm_cortexM4lf_math.lib(arm_common_tables.o)
    0x080104dc   0x080104dc   0x00008000   Data   RO         1154    .constdata          arm_cortexM4lf_math.lib(arm_common_tables.o)
    0x080184dc   0x080184dc   0x00000028   Data   RO         1216    .constdata          c_w.l(_printf_hex_int.o)
    0x08018504   0x08018504   0x00000094   Data   RO         1418    .constdata          c_w.l(bigflt0.o)
    0x08018598   0x08018598   0x00000079   Data   RO          573    .conststring        touch.o
    0x08018611   0x08018611   0x00000003   PAD
    0x08018614   0x08018614   0x00000020   Data   RO         1598    Region$$Table       anon$$obj.o
    0x08018634   0x08018634   0x0000001c   Data   RO         1446    locale$$data        c_w.l(lc_numeric_c.o)


    Execution Region RW_IRAM1 (Exec base: 0x20000000, Load base: 0x08018650, Size: 0x0001ef80, Max: 0x00020000, ABSOLUTE)

    Exec Addr    Load Addr    Size         Type   Attr      Idx    E Section Name        Object

    0x20000000   0x08018650   0x00000005   Data   RW            6    .data               main.o
    0x20000005   0x08018655   0x00000003   PAD
    0x20000008   0x08018658   0x00000004   Data   RW          219    .data               stm32f4xx_it.o
    0x2000000c   0x0801865c   0x00000014   Data   RW          270    .data               system_stm32f4xx.o
    0x20000020   0x08018670   0x00000001   Data   RW          316    .data               key.o
    0x20000021   0x08018671   0x00000001   PAD
    0x20000022   0x08018672   0x00000004   Data   RW          381    .data               lcd.o
    0x20000026   0x08018676   0x00000007   Data   RW          501    .data               ft5206.o
    0x2000002d   0x0801867d   0x00000001   Data   RW          525    .data               gt9147.o
    0x2000002e   0x0801867e   0x00000001   Data   RW          549    .data               ott2001a.o
    0x2000002f   0x0801867f   0x00000001   PAD
    0x20000030   0x08018680   0x00000036   Data   RW          574    .data               touch.o
    0x20000066   0x080186b6   0x00000002   PAD
    0x20000068   0x080186b8   0x0000002d   Data   RW          648    .data               ui.o
    0x20000095   0x080186e5   0x00000001   Data   RW          676    .data               control.o
    0x20000096   0x080186e6   0x00000002   PAD
    0x20000098   0x080186e8   0x00000020   Data   RW          741    .data               dsp_process.o
    0x200000b8   0x08018708   0x00000004   Data   RW          774    .data               delay.o
    0x200000bc   0x0801870c   0x00000006   Data   RW          815    .data               usart.o
    0x200000c2   0x08018712   0x00000010   Data   RW          915    .data               stm32f4xx_rcc.o
    0x200000d2   0x08018722   0x00000002   PAD
    0x200000d4        -       0x000166f0   Zero   RW            4    .bss                main.o
    0x200167c4        -       0x0000000e   Zero   RW          379    .bss                lcd.o
    0x200167d2   0x08018722   0x00000002   PAD
    0x200167d4        -       0x0000006c   Zero   RW          646    .bss                ui.o
    0x20016840        -       0x00008014   Zero   RW          740    .bss                dsp_process.o
    0x2001e854        -       0x000000c8   Zero   RW          814    .bss                usart.o
    0x2001e91c        -       0x00000060   Zero   RW         1464    .bss                c_w.l(libspace.o)
    0x2001e97c   0x08018722   0x00000004   PAD
    0x2001e980        -       0x00000200   Zero   RW          843    HEAP                startup_stm32f40_41xxx.o
    0x2001eb80        -       0x00000400   Zero   RW          842    STACK               startup_stm32f40_41xxx.o


==============================================================================

Image component sizes


      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   Object Name

       378          0          0          0          0       2850   24cxx.o
       988         36          0          0          0       3335   ad9833.o
       388         34          0          0          0       1231   adc_dma_timer.o
        60          6          0          1          0       1200   control.o
       600         24          0          0          0       2109   ctiic.o
       260          8          0          4          0       1509   delay.o
      2248        330          0         32      32788      19668   dsp_process.o
      1428         80         20          7          0       3011   ft5206.o
      1320        106        194          1          0       3230   gt9147.o
       188         14          0          1          0        840   key.o
     15752        150       6188          4         14      21360   lcd.o
        64          4          0          0          0        539   led.o
       216         90          0          5      91888     319623   main.o
       152          4          0          0          0        906   mcp41010.o
       224         20          0          0          0       1849   misc.o
       604         14          0          0          0       1936   myiic.o
       904         66         10          1          0       2795   ott2001a.o
       108         12       1144          0          0       1644   search.o
        64         26        392          0       1536        848   startup_stm32f40_41xxx.o
      1124         24          0          0          0      10718   stm32f4xx_adc.o
       936         32          0          0          0       6421   stm32f4xx_dma.o
      1480         18         28          0          0       5694   stm32f4xx_fsmc.o
       660         44          0          0          0       4189   stm32f4xx_gpio.o
       348         68          0          4          0       1915   stm32f4xx_it.o
      1628         52          0         16          0      13080   stm32f4xx_rcc.o
      3234         60          0          0          0      23044   stm32f4xx_tim.o
      1108         34          0          0          0       7912   stm32f4xx_usart.o
       528         46          0         20          0       1823   system_stm32f4xx.o
       112          4          0          0          0        832   timer.o
      4460        230        125         54          0       7005   touch.o
      3944        680         16         45        108       5864   ui.o
       332         18          0          6        200       3378   usart.o

    ----------------------------------------------------------------------
     45844       <USER>       <GROUP>        212     126536     482358   Object Totals
         0          0         32          0          0          0   (incl. Generated)
         4          0          7         11          2          0   (incl. Padding)

    ----------------------------------------------------------------------

      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   Library Member Name

       486          0          0          0          0       2210   arm_bitreversal.o
      1696          0          0          0          0      19932   arm_cfft_radix4_f32.o
       192         46          0          0          0        767   arm_cfft_radix4_init_f32.o
         0          0      34816          0          0       2517   arm_common_tables.o
         8          0          0          0          0         68   __main.o
       284          0          0          0          0        156   __printf_wp.o
         0          0          0          0          0          0   __rtentry.o
        12          0          0          0          0          0   __rtentry2.o
         6          0          0          0          0          0   __rtentry4.o
        52          8          0          0          0          0   __scatter.o
        26          0          0          0          0          0   __scatter_copy.o
        28          0          0          0          0          0   __scatter_zi.o
        44          0          0          0          0        108   _printf_char.o
        48          6          0          0          0         96   _printf_char_common.o
        36          4          0          0          0         80   _printf_char_file.o
         6          0          0          0          0          0   _printf_d.o
       120         16          0          0          0         92   _printf_dec.o
         6          0          0          0          0          0   _printf_f.o
      1054          0          0          0          0        216   _printf_fp_dec.o
       128         16          0          0          0         84   _printf_fp_infnan.o
        88          4         40          0          0         88   _printf_hex_int.o
       178          0          0          0          0         88   _printf_intcommon.o
         0          0          0          0          0          0   _printf_percent.o
         4          0          0          0          0          0   _printf_percent_end.o
         6          0          0          0          0          0   _printf_s.o
        82          0          0          0          0         80   _printf_str.o
         6          0          0          0          0          0   _printf_x.o
        22          0          0          0          0        100   _rserrno.o
        10          0          0          0          0         68   _sputc.o
       228          4        148          0          0         96   bigflt0.o
      1936        128          0          0          0        672   btod.o
        18          0          0          0          0         80   exit.o
         8          0          0          0          0         68   ferror.o
         6          0          0          0          0        152   heapauxi.o
        44         10         28          0          0         76   lc_numeric_c.o
         2          0          0          0          0          0   libinit.o
        22          0          0          0          0          0   libinit2.o
         2          0          0          0          0          0   libshutdown.o
         2          0          0          0          0          0   libshutdown2.o
         8          4          0          0         96         68   libspace.o
       138          0          0          0          0         80   lludiv10.o
        24          4          0          0          0         84   noretval__2printf.o
        40          6          0          0          0         84   noretval__2sprintf.o
         8          4          0          0          0         68   rt_errno_addr_intlibspace.o
         8          4          0          0          0         68   rt_locale_intlibspace.o
        68          0          0          0          0         68   rt_memclr.o
        78          0          0          0          0         80   rt_memclr_w.o
         2          0          0          0          0          0   rtexit.o
        10          0          0          0          0          0   rtexit2.o
        24          0          0          0          0         68   strcat.o
       128          0          0          0          0         68   strcmpv7m.o
        74          0          0          0          0         80   sys_stackheap_outer.o
         2          0          0          0          0         68   use_no_semi.o
         2          0          0          0          0         68   use_no_semi_2.o
       826         16          0          0          0        492   daddsub_clz.o
        24          0          0          0          0        116   dcmpi.o
       688        140          0          0          0        256   ddiv.o
        94          4          0          0          0        140   dfix.o
        90          4          0          0          0        140   dfixu.o
        84          0          0          0          0        232   dflt_clz.o
       120          4          0          0          0        140   dleqf.o
       340         12          0          0          0        152   dmul.o
       156          4          0          0          0        140   dnaninf.o
        12          0          0          0          0        116   dretinf.o
       108          0          0          0          0        128   drleqf.o
       408         56          0          0          0        168   dsqrt_umaal.o
        86          4          0          0          0        132   f2d.o
       140          4          0          0          0        132   fnaninf.o
        10          0          0          0          0        116   fpinit.o
         4          0          0          0          0        116   printf1.o
         0          0          0          0          0          0   usenofp.o
        48          0          0          0          0        124   fpclassify.o
       122          0          0          0          0        148   sqrt.o

    ----------------------------------------------------------------------
     10888        <USER>      <GROUP>          0        100      31834   Library Totals
        18          0          0          0          4          0   (incl. Padding)

    ----------------------------------------------------------------------

      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   Library Name

      2374         46      34816          0          0      25426   arm_cortexM4lf_math.lib
      5136        218        216          0         96       3420   c_w.l
      3190        248          0          0          0       2716   fz_wm.l
       170          0          0          0          0        272   m_wm.l

    ----------------------------------------------------------------------
     10888        <USER>      <GROUP>          0        100      31834   Library Totals

    ----------------------------------------------------------------------

==============================================================================


      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   

     56732       2846      43188        212     126636     504268   Grand Totals
     56732       2846      43188        212     126636     504268   ELF Image Totals
     56732       2846      43188        212          0          0   ROM Totals

==============================================================================

    Total RO  Size (Code + RO Data)                99920 (  97.58kB)
    Total RW  Size (RW Data + ZI Data)            126848 ( 123.88kB)
    Total ROM Size (Code + RO Data + RW Data)     100132 (  97.79kB)

==============================================================================

