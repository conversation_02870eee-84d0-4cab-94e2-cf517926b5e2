# ADC3 PA7采样功能说明

## 功能概述

本次修改在现有代码基础上添加了ADC3的PA7口采样功能，采样频率为204800Hz。当用户在LCD的Advanced Mode下按下"Reproduce Signal"按钮时，系统会启动ADC3采样。

## 主要修改内容

### 1. 硬件配置
- **GPIO配置**: PA7配置为模拟输入模式
- **ADC3配置**: 12位分辨率，单次转换，外部触发
- **定时器配置**: TIM4配置为204800Hz的PWM输出，通过CC4通道触发ADC3
- **DMA配置**: DMA2 Stream1用于ADC3数据传输，支持双缓冲模式

### 2. 文件修改列表

#### HARDWARE/ADC/adc_dma_timer.h
- 添加ADC3相关常量定义
- 添加ADC3相关函数声明

#### HARDWARE/ADC/adc_dma_timer.c
- 实现ADC3_GPIO_Init(): 初始化PA7为模拟输入
- 实现ADC3_Timer_Init(): 配置TIM4为204800Hz PWM输出
- 实现ADC3_Config(): 配置ADC3和DMA2 Stream1
- 实现ADC3_Start_Sampling(): 启动ADC3采样
- 实现ADC3_Stop_Sampling(): 停止ADC3采样

#### HARDWARE/UI/ui.c
- 添加UI_STATE_ADC3_SAMPLING状态
- 在reproduce按钮处理中添加ADC3采样逻辑
- 添加ADC3采样完成后的状态显示

#### USER/main.c
- 添加ADC3缓冲区变量声明
- 在初始化中调用ADC3相关初始化函数

#### USER/stm32f4xx_it.c
- 添加DMA2_Stream1_IRQHandler中断处理函数

## 使用方法

1. **硬件连接**: 将需要采样的信号连接到PA7引脚
2. **启动采样**: 
   - 进入Advanced Mode
   - 按下"Reproduce Signal"按钮
   - 系统显示"Status: ADC3 Sampling..."
3. **采样完成**: 
   - 系统自动停止采样
   - LCD显示采样完成信息，包括采样点数、采样频率和通道信息

## 技术参数

- **采样通道**: PA7 (ADC3_IN7)
- **采样频率**: 204800 Hz
- **采样精度**: 12位
- **缓冲区大小**: 4096个采样点
- **DMA模式**: 双缓冲循环模式
- **触发源**: TIM4 CC4 (PWM模式)

## 数据访问

采样数据存储在以下全局变量中：
```c
extern volatile uint16_t adc3_buf[2][ADC3_BUF_SIZE];  // 双缓冲区
extern volatile uint8_t adc3_capture_done;            // 采样完成标志
extern volatile uint8_t adc3_current_buf;             // 当前缓冲区索引
```

## 注意事项

1. **引脚配置**: 确保PA7没有被其他功能占用
2. **采样频率**: 204800Hz的采样频率需要确保输入信号的频率不超过奈奎斯特频率(102400Hz)
3. **缓冲区管理**: 采样使用双缓冲模式，需要及时处理采样完成的数据
4. **中断优先级**: DMA2_Stream1中断优先级设置为2,1，确保不与其他重要中断冲突

## 扩展建议

1. **数据处理**: 可以在采样完成后添加FFT分析或其他信号处理算法
2. **存储功能**: 可以添加将采样数据保存到外部存储器的功能
3. **实时显示**: 可以在LCD上实时显示采样波形
4. **参数调整**: 可以添加UI界面来调整采样频率和缓冲区大小
