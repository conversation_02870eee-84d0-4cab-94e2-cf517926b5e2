..\obj\main.o: main.c
..\obj\main.o: ..\SYSTEM\sys\sys.h
..\obj\main.o: ..\USER\stm32f4xx.h
..\obj\main.o: ..\CORE\core_cm4.h
..\obj\main.o: D:\Keil5\ARM\ARMCC\Bin\..\include\stdint.h
..\obj\main.o: ..\DSP_LIB\Include\core_cmInstr.h
..\obj\main.o: ..\DSP_LIB\Include\core_cmFunc.h
..\obj\main.o: ..\CORE\core_cm4_simd.h
..\obj\main.o: ..\USER\system_stm32f4xx.h
..\obj\main.o: ..\USER\stm32f4xx_conf.h
..\obj\main.o: ..\FWLIB\inc\stm32f4xx_adc.h
..\obj\main.o: ..\USER\stm32f4xx.h
..\obj\main.o: ..\FWLIB\inc\stm32f4xx_crc.h
..\obj\main.o: ..\FWLIB\inc\stm32f4xx_dbgmcu.h
..\obj\main.o: ..\FWLIB\inc\stm32f4xx_dma.h
..\obj\main.o: ..\FWLIB\inc\stm32f4xx_exti.h
..\obj\main.o: ..\FWLIB\inc\stm32f4xx_flash.h
..\obj\main.o: ..\FWLIB\inc\stm32f4xx_gpio.h
..\obj\main.o: ..\FWLIB\inc\stm32f4xx_i2c.h
..\obj\main.o: ..\FWLIB\inc\stm32f4xx_iwdg.h
..\obj\main.o: ..\FWLIB\inc\stm32f4xx_pwr.h
..\obj\main.o: ..\FWLIB\inc\stm32f4xx_rcc.h
..\obj\main.o: ..\FWLIB\inc\stm32f4xx_rtc.h
..\obj\main.o: ..\FWLIB\inc\stm32f4xx_sdio.h
..\obj\main.o: ..\FWLIB\inc\stm32f4xx_spi.h
..\obj\main.o: ..\FWLIB\inc\stm32f4xx_syscfg.h
..\obj\main.o: ..\FWLIB\inc\stm32f4xx_tim.h
..\obj\main.o: ..\FWLIB\inc\stm32f4xx_usart.h
..\obj\main.o: ..\FWLIB\inc\stm32f4xx_wwdg.h
..\obj\main.o: ..\FWLIB\inc\misc.h
..\obj\main.o: ..\FWLIB\inc\stm32f4xx_cryp.h
..\obj\main.o: ..\FWLIB\inc\stm32f4xx_hash.h
..\obj\main.o: ..\FWLIB\inc\stm32f4xx_rng.h
..\obj\main.o: ..\FWLIB\inc\stm32f4xx_can.h
..\obj\main.o: ..\FWLIB\inc\stm32f4xx_dac.h
..\obj\main.o: ..\FWLIB\inc\stm32f4xx_dcmi.h
..\obj\main.o: ..\FWLIB\inc\stm32f4xx_fsmc.h
..\obj\main.o: ..\SYSTEM\delay\delay.h
..\obj\main.o: ..\SYSTEM\usart\usart.h
..\obj\main.o: D:\Keil5\ARM\ARMCC\Bin\..\include\stdio.h
..\obj\main.o: ..\HARDWARE\LED\led.h
..\obj\main.o: ..\HARDWARE\TIMER\timer.h
..\obj\main.o: D:\Keil5\ARM\ARMCC\Bin\..\include\math.h
..\obj\main.o: ..\DSP_LIB\Include\arm_math.h
..\obj\main.o: ..\DSP_LIB\Include\core_cm4.h
..\obj\main.o: D:\Keil5\ARM\ARMCC\Bin\..\include\string.h
..\obj\main.o: ..\HARDWARE\ADC\adc_dma_timer.h
..\obj\main.o: ..\HARDWARE\LCD\lcd.h
..\obj\main.o: D:\Keil5\ARM\ARMCC\Bin\..\include\stdlib.h
..\obj\main.o: ..\HARDWARE\LCD\image.h
..\obj\main.o: D:\Keil5\ARM\ARMCC\Bin\..\include\float.h
..\obj\main.o: ..\HARDWARE\KEY\key.h
..\obj\main.o: ..\HARDWARE\AD9833\ad9833.h
..\obj\main.o: ..\HARDWARE\MCP41010\MCP41010.h
..\obj\main.o: ..\HARDWARE\TOUCH\touch.h
..\obj\main.o: ..\HARDWARE\TOUCH\ott2001a.h
..\obj\main.o: ..\HARDWARE\TOUCH\gt9147.h
..\obj\main.o: ..\HARDWARE\TOUCH\ft5206.h
..\obj\main.o: ..\HARDWARE\UI\ui.h
..\obj\main.o: ..\HARDWARE\CONTROL\control.h
..\obj\main.o: ..\HARDWARE\FILTER\filter.h
..\obj\main.o: ..\HARDWARE\DAC\dac.h
..\obj\main.o: ..\HARDWARE\dsp_process\dsp_process.h
