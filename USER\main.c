#include "sys.h"
#include "delay.h"  
#include "usart.h"  
#include "led.h"
#include "timer.h" 
#include "math.h" 
#include "arm_math.h" 
#include "adc_dma_timer.h"
#include "lcd.h"
#include "image.h"
#include <float.h> // ????FLT_MAX????
#include "key.h"
#include "ad9833.h"
#include "MCP41010.h"
#include "touch.h" 
#include "ui.h" 
#include "control.h"
#include "filter.h"/* --- 2. ?????????? --- */
#include "dac.h"
#include "dsp_process.h"

// ????????
//IIR_Filter_TypeDef my_lpf; 


// ???????????
#define FREQ_RES (SAM_FRE / (float)FFT_LENGTH)  // 62.5Hz

// ?????100kHz?????????
#define IF_FREQ 100000
#define TARGET_BIN (int)(IF_FREQ / FREQ_RES)  // ??????????(1600)


// ????????
//void calculate_basic(void); // ????????????

unsigned int Window_Num = 0;                // ?????????????
//unsigned int Flag_Capture_Enable = 0; 		//???????????????

float Window[FFT_LENGTH];                   // ??????????
//u16 ADC1_ConvertedValue[ADC1_DMA_Size];     // ADC????????
// ????????????????????��
volatile uint32_t adc_dual_buf[2][ADC_DMA_BUF_SIZE];
volatile uint16_t vin_buf[2][FFT_LENGTH];
volatile uint16_t vout_buf[2][FFT_LENGTH];
volatile uint8_t g_adc_capture_done = 0;

// ADC3????????
extern volatile uint16_t adc3_buf[2][ADC3_BUF_SIZE];
extern volatile uint8_t adc3_capture_done;
extern volatile uint8_t adc3_current_buf;

// ADC3���Ժ���
void ADC3_Test(void);
void ADC3_Print_Sample_Data(void);

// ===>>> ???????????????????????��???? <<<===
float gain_results[NUM_SWEEP_POINTS];
float filtered_gain_results[NUM_SWEEP_POINTS]; // ??????????
//float phase_results[NUM_SWEEP_POINTS]; // ?????????????????????

//float fft_inputbuf[FFT_LENGTH*2];           // FFT????????
//float fft_outputbuf[FFT_LENGTH];            // FFT???????
//float fft_backup[FFT_LENGTH/2];             /* ???????????????????????? */

/* --- 2. ?????????? --- */
// ????????
//IIR_Filter_TypeDef my_lpf; 


int main(void)
{

    /* --- ??????? (??????????????) --- */
    NVIC_PriorityGroupConfig(NVIC_PriorityGroup_2);
    delay_init(168);
    uart_init(115200);
    LED_Init();
//    ADC_GPIO_Init();
//    ADC_Config();
//    TIM3_Config(SAM_FRE);
    LCD_Init();
    KEY_Init();
	// ?????????????????
    TP_Init();
    // ??????????UI???????????
    UI_Init();
	Control_Init();
	SPI1_Init();
		AD9833_AmpSet_1(200);
		AD9833_1_WaveSeting(10000,0,SIN_WAVE,0);
	DSP_Init();
	 // ?????ADC??????????
    ADC_GPIO_Init();
    ADC_Config();
    ADC_Trig_Timer_Init();
    printf("Dual ADC system initialized.\r\n");

    // ?????ADC3??
    ADC3_GPIO_Init();
    ADC3_Timer_Init();
    ADC3_Config();
    printf("ADC3 system initialized.\r\n");
	    // ?????????????��????????????ADC???
    DMA_Cmd(DMA2_Stream0, ENABLE);
    TIM_Cmd(TIM3, ENABLE);
    printf("ADC Capture running in background.\r\n");
	
	    // ?????ADC, DMA??????????

    //TIM3_Config(SAM_FRE); // ??????????48kHz???????

    // ?????DAC???1
    //Dac1_Init();
    
//    /* --- ?????????? (??????????????) --- */
//    for (Window_Num = 0; Window_Num < FFT_LENGTH; Window_Num++) {
//        Window[Window_Num] = 0.54 - 0.46 * cos(2 * PI * (double)Window_Num / (FFT_LENGTH - 1));
//    }

        // ?????????????????????
    // ???????????1kHz?????????48kHz
//    IIR_LPF_Init(&my_lpf, 48000.0f, 1000.0f);
    
//    printf("System init OK. Digital filter test is running...\r\n");


	
	

    /* --- ????? (???????????) --- */
        /* --- ????? --- */
    while (1)
    {
        // ????????????????????UI????
        Control_Handler(); 
        UI_Handler();      
      
    }
}
//



//void calculate_basic(void)
//{
//	
//	unsigned int i;

//	
//	// ?????FFT
//	arm_cfft_radix2_instance_f32 scfft;
//	arm_cfft_radix2_init_f32(&scfft, FFT_LENGTH, 0, 1);
//	
//	// ???????
//	for(i = 0; i < FFT_LENGTH; i++) {
//		fft_inputbuf[2*i]   = (float)ADC1_ConvertedValue[i] * Window[i];   // ??????
//		fft_inputbuf[2*i+1] = 0.0f;  // ?��????
//	}

//	arm_cfft_radix2_f32(&scfft, fft_inputbuf);   // ??��?2FFT
//	arm_cmplx_mag_f32(fft_inputbuf, fft_outputbuf, FFT_LENGTH);   // ?????????

//	// ??????????
//	for(i = 0; i < 10; i++) fft_outputbuf[i] = 0;
//	
//	// ==== ??????????????? ====
//	

//}

/**
 * @brief  ADC3���Ժ���
 */
void ADC3_Test(void)
{
    printf("Starting ADC3 test...\r\n");

    // ����ADC3����
    ADC3_Start_Sampling();

    // �ȴ�����ɣ���ʱ5��
    uint32_t timeout_count = 500; // 500 * 10ms = 5��

    while(adc3_capture_done == 0 && timeout_count > 0) {
        delay_ms(10);
        timeout_count--;
    }

    if(adc3_capture_done > 0) {
        printf("ADC3 sampling completed!\r\n");
        ADC3_Print_Sample_Data();
    } else {
        printf("ADC3 sampling timeout!\r\n");
    }

    // ֹͣ����
    ADC3_Stop_Sampling();
}

/**
 * @brief  ��ӡADC3������ݵ�ǰ10������
 */
void ADC3_Print_Sample_Data(void)
{
    uint8_t buf_index = (adc3_current_buf == 0) ? 1 : 0; // ʹ�����һ�������

    printf("ADC3 Sample Data (first 10 samples from buffer %d):\r\n", buf_index);
    for(int i = 0; i < 10 && i < ADC3_BUF_SIZE; i++) {
        printf("Sample[%d] = %d (%.2fV)\r\n", i, adc3_buf[buf_index][i],
               (float)adc3_buf[buf_index][i] * 3.3f / 4095.0f);
    }

    // ����ͳ����Ϣ
    uint32_t sum = 0;
    uint16_t min_val = 4095, max_val = 0;
    for(int i = 0; i < ADC3_BUF_SIZE; i++) {
        uint16_t val = adc3_buf[buf_index][i];
        sum += val;
        if(val < min_val) min_val = val;
        if(val > max_val) max_val = val;
    }

    float avg = (float)sum / ADC3_BUF_SIZE;
    printf("Statistics: Min=%d, Max=%d, Avg=%.1f\r\n", min_val, max_val, avg);
    printf("Voltage range: %.3fV - %.3fV, Avg=%.3fV\r\n",
           min_val * 3.3f / 4095.0f, max_val * 3.3f / 4095.0f, avg * 3.3f / 4095.0f);
}


//



