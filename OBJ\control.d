..\obj\control.o: ..\HARDWARE\CONTROL\control.c
..\obj\control.o: ..\HARDWARE\CONTROL\control.h
..\obj\control.o: ..\HARDWARE\KEY\key.h
..\obj\control.o: ..\SYSTEM\sys\sys.h
..\obj\control.o: ..\USER\stm32f4xx.h
..\obj\control.o: ..\CORE\core_cm4.h
..\obj\control.o: D:\Keil5\ARM\ARMCC\Bin\..\include\stdint.h
..\obj\control.o: ..\DSP_LIB\Include\core_cmInstr.h
..\obj\control.o: ..\DSP_LIB\Include\core_cmFunc.h
..\obj\control.o: ..\CORE\core_cm4_simd.h
..\obj\control.o: ..\USER\system_stm32f4xx.h
..\obj\control.o: ..\USER\stm32f4xx_conf.h
..\obj\control.o: ..\FWLIB\inc\stm32f4xx_adc.h
..\obj\control.o: ..\USER\stm32f4xx.h
..\obj\control.o: ..\FWLIB\inc\stm32f4xx_crc.h
..\obj\control.o: ..\FWLIB\inc\stm32f4xx_dbgmcu.h
..\obj\control.o: ..\FWLIB\inc\stm32f4xx_dma.h
..\obj\control.o: ..\FWLIB\inc\stm32f4xx_exti.h
..\obj\control.o: ..\FWLIB\inc\stm32f4xx_flash.h
..\obj\control.o: ..\FWLIB\inc\stm32f4xx_gpio.h
..\obj\control.o: ..\FWLIB\inc\stm32f4xx_i2c.h
..\obj\control.o: ..\FWLIB\inc\stm32f4xx_iwdg.h
..\obj\control.o: ..\FWLIB\inc\stm32f4xx_pwr.h
..\obj\control.o: ..\FWLIB\inc\stm32f4xx_rcc.h
..\obj\control.o: ..\FWLIB\inc\stm32f4xx_rtc.h
..\obj\control.o: ..\FWLIB\inc\stm32f4xx_sdio.h
..\obj\control.o: ..\FWLIB\inc\stm32f4xx_spi.h
..\obj\control.o: ..\FWLIB\inc\stm32f4xx_syscfg.h
..\obj\control.o: ..\FWLIB\inc\stm32f4xx_tim.h
..\obj\control.o: ..\FWLIB\inc\stm32f4xx_usart.h
..\obj\control.o: ..\FWLIB\inc\stm32f4xx_wwdg.h
..\obj\control.o: ..\FWLIB\inc\misc.h
..\obj\control.o: ..\FWLIB\inc\stm32f4xx_cryp.h
..\obj\control.o: ..\FWLIB\inc\stm32f4xx_hash.h
..\obj\control.o: ..\FWLIB\inc\stm32f4xx_rng.h
..\obj\control.o: ..\FWLIB\inc\stm32f4xx_can.h
..\obj\control.o: ..\FWLIB\inc\stm32f4xx_dac.h
..\obj\control.o: ..\FWLIB\inc\stm32f4xx_dcmi.h
..\obj\control.o: ..\FWLIB\inc\stm32f4xx_fsmc.h
..\obj\control.o: ..\HARDWARE\UI\ui.h
