#include "adc_dma_timer.h"

// ??????main.c??????????????????
extern volatile uint32_t adc_dual_buf[2][ADC_DMA_BUF_SIZE];

// ADC3????????
volatile uint16_t adc3_buf[2][ADC3_BUF_SIZE];
volatile uint8_t adc3_capture_done = 0;
volatile uint8_t adc3_current_buf = 0;

/**
 * @brief  ??????ADC????GPIO???? (PA5 -> ADC2_IN5, PA6 -> ADC1_IN6)
 */
void ADC_GPIO_Init(void)
{
	GPIO_InitTypeDef GPIO_InitStructure;
    
    // ===>>> ????????????????????? GPIOA ????? <<<===
	RCC_AHB1PeriphClockCmd(RCC_AHB1Periph_GPIOA, ENABLE); 
	
	// ===>>> ????????????????????? PA5 ?? PA6 <<<===
    // ?????? PA5 ?? PA6
	GPIO_InitStructure.GPIO_Pin = GPIO_Pin_5 | GPIO_Pin_6;
	GPIO_InitStructure.GPIO_Mode = GPIO_Mode_AN;
	GPIO_InitStructure.GPIO_PuPd = GPIO_PuPd_NOPULL;
	GPIO_Init(GPIOA, &GPIO_InitStructure);
}
 
/**
 * @brief  ???????U????TIM3???ADC??????????
 * @note   ????????? adc_dma_timer.h ???? SAM_FRE ???????
 */
void ADC_Trig_Timer_Init(void)
{
	TIM_TimeBaseInitTypeDef TIM_TimeBaseStructure;

    // TIM3 ????APB1???????????????? 84MHz
    // ??????? F_trig = 84,000,000 / (Period + 1)
    // Period = 84,000,000 / F_trig - 1
    
    // ===>>> ???????Period ???????????? SAM_FRE ?? <<<===
    uint32_t period = 84000000 / SAM_FRE - 1;

    // ?????????????16???????? (TIM3??16???????)
    if (period > 65535) {
        // ??????????period???????????????84MHz??????????????????????
        // ???????????????????????????????LED
        period = 65535; 
    }
	
	RCC_APB1PeriphClockCmd(RCC_APB1Periph_TIM3, ENABLE);
	
	TIM_TimeBaseStructure.TIM_Period = (uint16_t)period; // ???????16??
	TIM_TimeBaseStructure.TIM_Prescaler = 0;
	TIM_TimeBaseStructure.TIM_ClockDivision = 0;
	TIM_TimeBaseStructure.TIM_CounterMode = TIM_CounterMode_Up;
	TIM_TimeBaseInit(TIM3, &TIM_TimeBaseStructure);
	
	TIM_SelectOutputTrigger(TIM3, TIM_TRGOSource_Update);
    
    // ?????????????????????????TIM?????????????
}
 
/**
 * @brief  ????ADC1??ADC2?????????????????DMA????????
 */
void ADC_Config(void)
{
	ADC_InitTypeDef       ADC_InitStructure;
	ADC_CommonInitTypeDef ADC_CommonInitStructure;
	DMA_InitTypeDef       DMA_InitStructure;
	NVIC_InitTypeDef 	  NVIC_InitStructure;
 
	// ??????
	RCC_AHB1PeriphClockCmd(RCC_AHB1Periph_DMA2, ENABLE); 
	RCC_APB2PeriphClockCmd(RCC_APB2Periph_ADC1 | RCC_APB2Periph_ADC2, ENABLE);
	
	// ????DMA????
	NVIC_InitStructure.NVIC_IRQChannel = DMA2_Stream0_IRQn;
	NVIC_InitStructure.NVIC_IRQChannelPreemptionPriority = 1;
	NVIC_InitStructure.NVIC_IRQChannelSubPriority = 1;
	NVIC_InitStructure.NVIC_IRQChannelCmd = ENABLE;
	NVIC_Init(&NVIC_InitStructure);     
 
	/* --- ???? DMA2 Stream0 (????ADC?? + ??????) --- */
    DMA_DeInit(DMA2_Stream0);
    DMA_DoubleBufferModeConfig(DMA2_Stream0, (uint32_t)adc_dual_buf[1], DMA_Memory_0);
    DMA_DoubleBufferModeCmd(DMA2_Stream0, ENABLE);

	DMA_InitStructure.DMA_Channel = DMA_Channel_0;
	DMA_InitStructure.DMA_PeripheralBaseAddr = (uint32_t)&(ADC->CDR);
	DMA_InitStructure.DMA_Memory0BaseAddr = (uint32_t)adc_dual_buf[0];
	DMA_InitStructure.DMA_DIR = DMA_DIR_PeripheralToMemory;
	DMA_InitStructure.DMA_BufferSize = ADC_DMA_BUF_SIZE;
	DMA_InitStructure.DMA_PeripheralInc = DMA_PeripheralInc_Disable;
	DMA_InitStructure.DMA_MemoryInc = DMA_MemoryInc_Enable;
	DMA_InitStructure.DMA_PeripheralDataSize = DMA_PeripheralDataSize_Word;
	DMA_InitStructure.DMA_MemoryDataSize = DMA_MemoryDataSize_Word;
	DMA_InitStructure.DMA_Mode = DMA_Mode_Circular;
	DMA_InitStructure.DMA_Priority = DMA_Priority_High;
    // ... ????DMA???? ...
	DMA_Init(DMA2_Stream0, &DMA_InitStructure);
	
    DMA_ITConfig(DMA2_Stream0, DMA_IT_TC | DMA_IT_HT, ENABLE);
 
	/* --- ADC ??????? (?ADC?????) --- */
	ADC_CommonInitStructure.ADC_Mode = ADC_DualMode_RegSimult;
	ADC_CommonInitStructure.ADC_Prescaler = ADC_Prescaler_Div4; // ??????
	ADC_CommonInitStructure.ADC_DMAAccessMode = ADC_DMAAccessMode_2;
	ADC_CommonInitStructure.ADC_TwoSamplingDelay = ADC_TwoSamplingDelay_5Cycles;
	ADC_CommonInit(&ADC_CommonInitStructure);
 
	/* --- ???? ADC1 (??) --- */
	ADC_InitStructure.ADC_Resolution = ADC_Resolution_12b;
	ADC_InitStructure.ADC_ScanConvMode = DISABLE;
	ADC_InitStructure.ADC_ContinuousConvMode = DISABLE;
	ADC_InitStructure.ADC_ExternalTrigConvEdge = ADC_ExternalTrigConvEdge_Rising;
    // ??????????????????????????????????? TIM3_TRGO
	ADC_InitStructure.ADC_ExternalTrigConv = ADC_ExternalTrigConv_T3_TRGO;
	ADC_InitStructure.ADC_DataAlign = ADC_DataAlign_Right;
	ADC_InitStructure.ADC_NbrOfConversion = 1;
	ADC_Init(ADC1, &ADC_InitStructure);
	    // ===>>> ?????????????ADC1?????? PA6 (ADC_Channel_6) <<<===
	ADC_RegularChannelConfig(ADC1, ADC_Channel_6, 1, ADC_SampleTime_3Cycles); // PA6 (V_out)
	//ADC_RegularChannelConfig(ADC1, ADC_Channel_10, 1, ADC_SampleTime_3Cycles); // PC0 (V_out)
 
    /* --- ???? ADC2 (??) --- */
    ADC_Init(ADC2, &ADC_InitStructure);
	ADC_RegularChannelConfig(ADC2, ADC_Channel_5, 1, ADC_SampleTime_3Cycles);  // PA5 (V_in)
	
    ADC_MultiModeDMARequestAfterLastTransferCmd(ENABLE);
    
	ADC_Cmd(ADC1, ENABLE);
	ADC_Cmd(ADC2, ENABLE);
}

/**
 * @brief  ?????ADC3????GPIO???? (PA7 -> ADC3_IN7)
 */
void ADC3_GPIO_Init(void)
{
	GPIO_InitTypeDef GPIO_InitStructure;

    // ???GPIOA?????
	RCC_AHB1PeriphClockCmd(RCC_AHB1Periph_GPIOA, ENABLE);

	// ????PA7??????
	GPIO_InitStructure.GPIO_Pin = GPIO_Pin_7;
	GPIO_InitStructure.GPIO_Mode = GPIO_Mode_AN;
	GPIO_InitStructure.GPIO_PuPd = GPIO_PuPd_NOPULL;
	GPIO_Init(GPIOA, &GPIO_InitStructure);
}

/**
 * @brief  ????TIM4???ADC3??????????
 * @note   ????????204800Hz
 */
void ADC3_Timer_Init(void)
{
	TIM_TimeBaseInitTypeDef TIM_TimeBaseStructure;

    // TIM4 ????APB1???????????????? 84MHz
    // ??????? F_trig = 84,000,000 / (Period + 1)
    // Period = 84,000,000 / F_trig - 1
    uint32_t period = 84000000 / ADC3_SAM_FRE - 1;

    // ?????????????16???????? (TIM4??16???????)
    if (period > 65535) {
        period = 65535;
    }

	RCC_APB1PeriphClockCmd(RCC_APB1Periph_TIM4, ENABLE);

	TIM_TimeBaseStructure.TIM_Period = (uint16_t)period;
	TIM_TimeBaseStructure.TIM_Prescaler = 0;
	TIM_TimeBaseStructure.TIM_ClockDivision = 0;
	TIM_TimeBaseStructure.TIM_CounterMode = TIM_CounterMode_Up;
	TIM_TimeBaseInit(TIM4, &TIM_TimeBaseStructure);

	// ????CC4???
	TIM_OCInitTypeDef TIM_OCInitStructure;
	TIM_OCInitStructure.TIM_OCMode = TIM_OCMode_PWM1;
	TIM_OCInitStructure.TIM_OutputState = TIM_OutputState_Enable;
	TIM_OCInitStructure.TIM_Pulse = period / 2; // 50%????
	TIM_OCInitStructure.TIM_OCPolarity = TIM_OCPolarity_High;
	TIM_OC4Init(TIM4, &TIM_OCInitStructure);

	TIM_OC4PreloadConfig(TIM4, TIM_OCPreload_Enable);
}

/**
 * @brief  ????ADC3????DMA
 */
void ADC3_Config(void)
{
	ADC_InitTypeDef       ADC_InitStructure;
	DMA_InitTypeDef       DMA_InitStructure;
	NVIC_InitTypeDef 	  NVIC_InitStructure;

	// ??????
	RCC_AHB1PeriphClockCmd(RCC_AHB1Periph_DMA2, ENABLE);
	RCC_APB2PeriphClockCmd(RCC_APB2Periph_ADC3, ENABLE);

	// ????DMA????
	NVIC_InitStructure.NVIC_IRQChannel = DMA2_Stream1_IRQn;
	NVIC_InitStructure.NVIC_IRQChannelPreemptionPriority = 2;
	NVIC_InitStructure.NVIC_IRQChannelSubPriority = 1;
	NVIC_InitStructure.NVIC_IRQChannelCmd = ENABLE;
	NVIC_Init(&NVIC_InitStructure);

	/* --- ???? DMA2 Stream1 (ADC3) --- */
    DMA_DeInit(DMA2_Stream1);
    DMA_DoubleBufferModeConfig(DMA2_Stream1, (uint32_t)adc3_buf[1], DMA_Memory_0);
    DMA_DoubleBufferModeCmd(DMA2_Stream1, ENABLE);

	DMA_InitStructure.DMA_Channel = DMA_Channel_2;
	DMA_InitStructure.DMA_PeripheralBaseAddr = (uint32_t)&(ADC3->DR);
	DMA_InitStructure.DMA_Memory0BaseAddr = (uint32_t)adc3_buf[0];
	DMA_InitStructure.DMA_DIR = DMA_DIR_PeripheralToMemory;
	DMA_InitStructure.DMA_BufferSize = ADC3_BUF_SIZE;
	DMA_InitStructure.DMA_PeripheralInc = DMA_PeripheralInc_Disable;
	DMA_InitStructure.DMA_MemoryInc = DMA_MemoryInc_Enable;
	DMA_InitStructure.DMA_PeripheralDataSize = DMA_PeripheralDataSize_HalfWord;
	DMA_InitStructure.DMA_MemoryDataSize = DMA_MemoryDataSize_HalfWord;
	DMA_InitStructure.DMA_Mode = DMA_Mode_Circular;
	DMA_InitStructure.DMA_Priority = DMA_Priority_High;
	DMA_InitStructure.DMA_FIFOMode = DMA_FIFOMode_Disable;
	DMA_InitStructure.DMA_FIFOThreshold = DMA_FIFOThreshold_HalfFull;
	DMA_InitStructure.DMA_MemoryBurst = DMA_MemoryBurst_Single;
	DMA_InitStructure.DMA_PeripheralBurst = DMA_PeripheralBurst_Single;
	DMA_Init(DMA2_Stream1, &DMA_InitStructure);

    DMA_ITConfig(DMA2_Stream1, DMA_IT_TC | DMA_IT_HT, ENABLE);

	/* --- ???? ADC3 --- */
	ADC_InitStructure.ADC_Resolution = ADC_Resolution_12b;
	ADC_InitStructure.ADC_ScanConvMode = DISABLE;
	ADC_InitStructure.ADC_ContinuousConvMode = DISABLE;
	ADC_InitStructure.ADC_ExternalTrigConvEdge = ADC_ExternalTrigConvEdge_Rising;
	ADC_InitStructure.ADC_ExternalTrigConv = ADC_ExternalTrigConv_T4_CC4;
	ADC_InitStructure.ADC_DataAlign = ADC_DataAlign_Right;
	ADC_InitStructure.ADC_NbrOfConversion = 1;
	ADC_Init(ADC3, &ADC_InitStructure);

	// ????ADC3???7 (PA7)
	ADC_RegularChannelConfig(ADC3, ADC_Channel_7, 1, ADC_SampleTime_3Cycles);

	// ???ADC3??DMA
	ADC_DMACmd(ADC3, ENABLE);
	ADC_DMARequestAfterLastTransferCmd(ADC3, ENABLE);

	ADC_Cmd(ADC3, ENABLE);
}

/**
 * @brief  ????ADC3????
 */
void ADC3_Start_Sampling(void)
{
	// ?????????
	adc3_capture_done = 0;
	adc3_current_buf = 0;

	// ???DMA
	DMA_Cmd(DMA2_Stream1, ENABLE);

	// ???????
	TIM_Cmd(TIM4, ENABLE);
}

/**
 * @brief  ??ADC3????
 */
void ADC3_Stop_Sampling(void)
{
	// ???????
	TIM_Cmd(TIM4, DISABLE);

	// ???DMA
	DMA_Cmd(DMA2_Stream1, DISABLE);
}
