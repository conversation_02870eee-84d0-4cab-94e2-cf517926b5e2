/**
 *********************************************************************************
 * @file    ui.c
 * <AUTHOR> Name
 * @version V2.0
 * @date    2024-xx-xx
 * @brief   User Interface (UI) module with Basic and Advanced modes.
 *********************************************************************************
 */

#include "ui.h"
#include "control.h"
#include "lcd.h"
#include "touch.h"
#include "ad9833.h"
#include "delay.h"
#include "search.h"
#include <stdio.h>
#include <math.h>
#include "dsp_process.h"
#include <string.h>
#include "adc_dma_timer.h"

/*
==================================================================================
*                                 Module-Private Defines & Variables
==================================================================================
*/

/* ================================================================= */

// �µġ�����ϸ��UI״̬��
typedef enum {
    UI_STATE_IDLE,          // ���У��ȴ�����
    UI_STATE_SWEEPING,      // ����ɨƵ
    UI_STATE_ANALYZING,     // ���ڷ�������
    UI_STATE_DISPLAYING,    // ������ʾ���ս��
    UI_STATE_ADC3_SAMPLING  // ADC3����״̬
} UI_Advanced_State_TypeDef;

static UI_Advanced_State_TypeDef advanced_ui_state = UI_STATE_IDLE;
/* ================================================================= */

// Button definition structure
typedef struct {
    uint16_t x, y, w, h;
    const char* text;
} Button_TypeDef;

// --- Module-private variables ---
// Basic Mode state variables
static double current_freq;
static const double freq_steps[] = {100.0, 10000.0}; 
static uint8_t step_index;
static double current_step;
static uint8_t is_at_1mhz;
static int target_v_out_x10;
static int direct_amp_value;
static uint8_t control_mode;

// Basic Mode buttons
static Button_TypeDef btn_freq_p, btn_freq_m, btn_step, btn_jump_f, btn_jump_a, btn_amp_p, btn_amp_m;

// Advanced Mode buttons
static Button_TypeDef btn_learn, btn_reproduce;

// ADC3��������
extern volatile uint16_t adc3_buf[2][ADC3_BUF_SIZE];
extern volatile uint8_t adc3_capture_done;
extern volatile uint8_t adc3_current_buf;

// --- Private function prototypes ---
// Public functions are declared in ui.h

// Basic Mode Helpers
static void UI_Draw_Basic_Mode_Screen(void);
static void UI_Update_Basic_Mode_Data(void);
static uint8_t UI_Get_Button_Pressed_Basic(void);
static void UI_Update_Hardware_State(void);
static void UI_Handler_Basic(void);
static void PGA_Set_Amp(unsigned char amp);

// Advanced Mode Helpers
static void UI_Draw_Advanced_Mode_Screen(void);
static void UI_Handler_Advanced(void);
static uint8_t UI_Get_Button_Pressed_Advanced(void);


/*
==================================================================================
*                                 Public Function Implementations
==================================================================================
*/

/**
 * @brief  Initializes the UI for both modes.
 */
void UI_Init(void)
{
    /* 1. Layout Calculations */
    const uint16_t btn_h = 40;
    const uint16_t row_gap = 10;
    uint16_t y1 = 170;
    const uint16_t btn_w1 = 60;
    const uint16_t x_margin1 = 20;
    const uint16_t x_gap1 = (240 - x_margin1 * 2 - btn_w1 * 3) / 2;
    uint16_t y2 = y1 + btn_h + row_gap;
    const uint16_t btn_w2 = 85;
    const uint16_t x_margin2 = 25;
    const uint16_t x_gap2 = 240 - x_margin2 * 2 - btn_w2 * 2;
    uint16_t y3 = y2 + btn_h + row_gap;
    const uint16_t btn_w3 = 85;
    const uint16_t x_margin3 = 25;
    const uint16_t x_gap3 = 240 - x_margin3 * 2 - btn_w3 * 2;

    /* 2. State Initialization */
    current_freq = 1000.0;
    step_index = 0;
    current_step = freq_steps[step_index];
    is_at_1mhz = 0;
    target_v_out_x10 = 20;
    direct_amp_value = 220;
    control_mode = 0;

    /* 3. Button Definitions */
    // Basic Mode Buttons
    btn_freq_p.x = x_margin1; btn_freq_p.y = y1; btn_freq_p.w = btn_w1; btn_freq_p.h = btn_h; btn_freq_p.text = "+";
    btn_freq_m.x = x_margin1 + btn_w1 + x_gap1; btn_freq_m.y = y1; btn_freq_m.w = btn_w1; btn_freq_m.h = btn_h; btn_freq_m.text = "-";
    btn_step.x   = x_margin1 + btn_w1 * 2 + x_gap1 * 2; btn_step.y = y1; btn_step.w = btn_w1; btn_step.h = btn_h; btn_step.text = "STEP";
    btn_jump_f.x = x_margin2; btn_jump_f.y = y2; btn_jump_f.w = btn_w2; btn_jump_f.h = btn_h; btn_jump_f.text = "JumpF";
    btn_jump_a.x = x_margin2 + btn_w2 + x_gap2; btn_jump_a.y = y2; btn_jump_a.w = btn_w2; btn_jump_a.h = btn_h; btn_jump_a.text = "Mode";
    btn_amp_p.x = x_margin3; btn_amp_p.y = y3; btn_amp_p.w = btn_w3; btn_amp_p.h = btn_h; btn_amp_p.text = "Amp+";
    btn_amp_m.x = x_margin3 + btn_w3 + x_gap3; btn_amp_m.y = y3; btn_amp_m.w = btn_w3; btn_amp_m.h = btn_h; btn_amp_m.text = "Amp-";
    
    // Advanced Mode Buttons
    btn_learn.x = 30; btn_learn.y = 100; btn_learn.w = 180; btn_learn.h = 50;
    btn_learn.text = "Learn & Model";
    btn_reproduce.x = 30; btn_reproduce.y = 180; btn_reproduce.w = 180; btn_reproduce.h = 50;
    btn_reproduce.text = "Reproduce Signal";
    
    /* 4. Draw Initial Screen */
    UI_Draw_Basic_Mode_Screen();
    UI_Update_Hardware_State();
}

/**
 * @brief  Main UI handler, dispatches to sub-handlers based on mode.
 */
void UI_Handler(void)
{
    if (Control_Get_Mode() == MODE_BASIC) {
        UI_Handler_Basic();
    } else {
        UI_Handler_Advanced();
    }
}

/**
 * @brief  Public function called by Control module to redraw the screen on mode switch.
 */
void UI_Mode_Switch(void)
{
    if (Control_Get_Mode() == MODE_BASIC) {
        UI_Draw_Basic_Mode_Screen();
    } else {
        UI_Draw_Advanced_Mode_Screen();
		        /* ========================================================== */
        /* ==                 �������޸ĵ� 2������                   == */
        /*        �л����߼�ģʽʱ������������״̬ΪIDLE         == */
        /* ========================================================== */
        advanced_ui_state = UI_STATE_IDLE;
    }
}


/*
==================================================================================
*                                 Module-Private Function Implementations
==================================================================================
*/

/**
 * @brief  Sets the PGA gain by simulating SPI.
 */
static void PGA_Set_Amp(unsigned char amp)
{
    unsigned char i;
    unsigned int temp;
   	
    CS_1 = 0;
    temp = 0x1100 | amp;
    for(i = 0; i < 16; i++) {
        SCLK_1 = 0;	
        if(temp & 0x8000) SDATA_1 = 1;
        else SDATA_1 = 0;
        temp <<= 1;
        SCLK_1 = 1;
    }
    CS_1 = 1;
}

// ================================================================================
// ==                          Basic Mode Functions                              ==
// ================================================================================

/**
 * @brief  Handles all touch events and logic for the Basic Mode UI.
 */
static void UI_Handler_Basic(void)
{
    uint8_t update_hw_needed = 0;
    uint8_t update_ui_needed = 0;
    uint8_t key_code = UI_Get_Button_Pressed_Basic();

    switch (key_code) {
        case 1: // Freq+
            current_freq += current_step;
            if (current_freq > 2000000.0) current_freq = 2000000.0;
            update_hw_needed = 1;
            update_ui_needed = 1;
            break;
        case 2: // Freq-
            current_freq -= current_step;
            if (current_freq < 100.0) current_freq = 100.0;
            update_hw_needed = 1;
            update_ui_needed = 1;
            break;
        case 3: // STEP
            step_index = (step_index + 1) % 2;
            current_step = freq_steps[step_index];
            update_ui_needed = 1;
            break;
        case 4: // JumpF
            is_at_1mhz = !is_at_1mhz;
            current_freq = is_at_1mhz ? 1000000.0 : 100.0;
            update_hw_needed = 1;
            update_ui_needed = 1;
            break;
        case 5: // Mode
            control_mode = !control_mode;
            update_hw_needed = 1;
            update_ui_needed = 1;
            break;
        case 6: // Amp+
            if (control_mode == 0) {
                target_v_out_x10++; if (target_v_out_x10 > 20) target_v_out_x10 = 20;
            } else {
                direct_amp_value++; if (direct_amp_value > 255) direct_amp_value = 255;
            }
            update_hw_needed = 1;
            update_ui_needed = 1;
            break;
        case 7: // Amp-
            if (control_mode == 0) {
                target_v_out_x10--; if (target_v_out_x10 < 10) target_v_out_x10 = 10;
            } else {
                direct_amp_value--; if (direct_amp_value < 0) direct_amp_value = 0;
            }
            update_hw_needed = 1;
            update_ui_needed = 1;
            break;
    }
    
    if (update_hw_needed) UI_Update_Hardware_State();
    if (update_ui_needed) {
        UI_Update_Basic_Mode_Data();
        delay_ms(150);
    }
}

/**
 * @brief  Scans and identifies which button is pressed in Basic Mode.
 */
static uint8_t UI_Get_Button_Pressed_Basic(void)
{
    TP_Scan(0); 
    if (tp_dev.sta & TP_PRES_DOWN) {
        uint16_t x = tp_dev.x[0], y = tp_dev.y[0];
        if (x > btn_freq_p.x && x < (btn_freq_p.x+btn_freq_p.w) && y > btn_freq_p.y && y < (btn_freq_p.y+btn_freq_p.h)) return 1;
        if (x > btn_freq_m.x && x < (btn_freq_m.x+btn_freq_m.w) && y > btn_freq_m.y && y < (btn_freq_m.y+btn_freq_m.h)) return 2;
        if (x > btn_step.x   && x < (btn_step.x+btn_step.w)   && y > btn_step.y   && y < (btn_step.y+btn_step.h))   return 3;
        if (x > btn_jump_f.x && x < (btn_jump_f.x+btn_jump_f.w) && y > btn_jump_f.y && y < (btn_jump_f.y+btn_jump_f.h)) return 4;
        if (x > btn_jump_a.x && x < (btn_jump_a.x+btn_jump_a.w) && y > btn_jump_a.y && y < (btn_jump_a.y+btn_jump_a.h)) return 5;
        if (x > btn_amp_p.x  && x < (btn_amp_p.x+btn_amp_p.w)  && y > btn_amp_p.y  && y < (btn_amp_p.y+btn_amp_p.h))  return 6;
        if (x > btn_amp_m.x  && x < (btn_amp_m.x+btn_amp_m.w)  && y > btn_amp_m.y  && y < (btn_amp_m.y+btn_amp_m.h))  return 7;
    }
    return 0; 
}

/**
 * @brief  Refreshes the 4-line data display in Basic Mode.
 */
static void UI_Update_Basic_Mode_Data(void)
{
    char buffer[30];
    BACK_COLOR = WHITE;
    LCD_Fill(100, 70, 239, 149, WHITE);
    POINT_COLOR = RED;
    LCD_ShowString(20, 70, 80, 16, 16, (u8*)"Freq :");
    sprintf(buffer, "%.0f Hz", current_freq);
    LCD_ShowString(100, 70, 140, 16, 16, (u8*)buffer);
    POINT_COLOR = BLACK;
    LCD_ShowString(20, 90, 80, 16, 16, (u8*)"Step :");
    sprintf(buffer, "%.0f Hz", current_step);
    LCD_ShowString(100, 90, 140, 16, 16, (u8*)buffer);
    POINT_COLOR = GREEN;
    LCD_ShowString(20, 110, 80, 16, 16, (u8*)"Mode :");
    if (control_mode == 0) sprintf(buffer, "Precise Ctrl"); else sprintf(buffer, "Direct Drive");
    LCD_ShowString(100, 110, 140, 16, 16, (u8*)buffer);
    POINT_COLOR = BLUE;
    if (control_mode == 0) {
        LCD_ShowString(20, 130, 80, 16, 16, (u8*)"Target:");
        sprintf(buffer, "%d.%dV", target_v_out_x10 / 10, target_v_out_x10 % 10);
    } else {
        LCD_ShowString(20, 130, 80, 16, 16, (u8*)"AmpVal:");
        sprintf(buffer, "%d", direct_amp_value);
    }
    LCD_ShowString(100, 130, 140, 16, 16, (u8*)buffer);
}

/**
 * @brief  Updates hardware (AD9833 & PGA) based on the current UI state.
 */
static void UI_Update_Hardware_State(void)
{
    unsigned char amp_to_set;
    float final_target_v_out;
    if (control_mode == 0) {
        final_target_v_out = (float)target_v_out_x10 / 10.0f;
        if (current_freq >= 100 && current_freq <= 3000) {
            amp_to_set = Search_Get_Best_Amp((unsigned int)current_freq, final_target_v_out);
        } else {
            amp_to_set = 255;
        }
    } else {
        amp_to_set = (unsigned char)direct_amp_value;
    }
    AD9833_1_WaveSeting(current_freq, 0, SIN_WAVE, 0);
    PGA_Set_Amp(amp_to_set);
}

/**
 * @brief  Draws the static UI frame for Basic Mode.
 */
static void UI_Draw_Basic_Mode_Screen(void)
{
    LCD_Clear(WHITE);
    POINT_COLOR = WHITE; BACK_COLOR = BLUE;
    LCD_Fill(0, 0, lcddev.width - 1, 29, BLUE);
    LCD_ShowString(70, 8, 240, 24, 16, (u8*)"Basic Mode");
    POINT_COLOR = BLACK; BACK_COLOR = WHITE;
    LCD_ShowString(20, 70, 80, 16, 16, (u8*)"Freq :");
    LCD_ShowString(20, 90, 80, 16, 16, (u8*)"Step :");
    LCD_ShowString(20, 110, 80, 16, 16, (u8*)"Mode :");
    LCD_ShowString(20, 130, 80, 16, 16, (u8*)"Ampl :");
    POINT_COLOR = DARKBLUE;
    LCD_DrawRectangle(btn_freq_p.x, btn_freq_p.y, btn_freq_p.x + btn_freq_p.w, btn_freq_p.y + btn_freq_p.h);
    LCD_ShowString(btn_freq_p.x + 25, btn_freq_p.y + 12, 16, 16, 16, (u8*)btn_freq_p.text);
    LCD_DrawRectangle(btn_freq_m.x, btn_freq_m.y, btn_freq_m.x + btn_freq_m.w, btn_freq_m.y + btn_freq_m.h);
    LCD_ShowString(btn_freq_m.x + 25, btn_freq_m.y + 12, 16, 16, 16, (u8*)btn_freq_m.text);
    LCD_DrawRectangle(btn_step.x, btn_step.y, btn_step.x + btn_step.w, btn_step.y + btn_step.h);
    LCD_ShowString(btn_step.x + 12, btn_step.y + 12, 40, 16, 16, (u8*)btn_step.text);
    LCD_DrawRectangle(btn_jump_f.x, btn_jump_f.y, btn_jump_f.x + btn_jump_f.w, btn_jump_f.y + btn_jump_f.h);
    LCD_ShowString(btn_jump_f.x + 20, btn_jump_f.y + 12, 60, 16, 16, (u8*)btn_jump_f.text);
    LCD_DrawRectangle(btn_jump_a.x, btn_jump_a.y, btn_jump_a.x + btn_jump_a.w, btn_jump_a.y + btn_jump_a.h);
    LCD_ShowString(btn_jump_a.x + 25, btn_jump_a.y + 12, 60, 16, 16, (u8*)btn_jump_a.text);
    LCD_DrawRectangle(btn_amp_p.x, btn_amp_p.y, btn_amp_p.x + btn_amp_p.w, btn_amp_p.y + btn_amp_p.h);
    LCD_ShowString(btn_amp_p.x + 20, btn_amp_p.y + 12, 60, 16, 16, (u8*)btn_amp_p.text);
    LCD_DrawRectangle(btn_amp_m.x, btn_amp_m.y, btn_amp_m.x + btn_amp_m.w, btn_amp_m.y + btn_amp_m.h);
    LCD_ShowString(btn_amp_m.x + 20, btn_amp_m.y + 12, 60, 16, 16, (u8*)btn_amp_m.text);
    UI_Update_Basic_Mode_Data();
}

// =================================================================================
// ==                          Advanced Mode Functions                            ==
// =================================================================================

// ui.c

// ui.c

/* ================================================================= */
/* ==            �������޸ĵ� 3���ô˺�����ȫ�滻�ɰ汾������       == */
/* ================================================================= */

static void UI_Handler_Advanced(void)
{
    /* C90 �������� */
    uint8_t key_code;
    char buffer[64];
    FilterType learned_type;

    switch(advanced_ui_state)
    {
        // ״̬1: ����
        case UI_STATE_IDLE:
            key_code = UI_Get_Button_Pressed_Advanced();
            if (key_code == 1) { // "Learn & Model" ������
                // a. ����Ļ�ϸ�����ʱ����
                LCD_Fill(0, 250, 239, 319, WHITE);
                LCD_ShowString(20, 250, 200, 16, 16, (u8*)"Status: Sweeping...");

                // b. ����ɨƵ (������)
                Perform_Frequency_Sweep_Start();

                // c. �л�����һ��״̬
                advanced_ui_state = UI_STATE_SWEEPING;
            }
            else if (key_code == 2) { // "Reproduce Signal" ������
                // a. ����Ļ�ϸ�����ʱ����
                LCD_Fill(0, 250, 239, 319, WHITE);
                LCD_ShowString(20, 250, 200, 16, 16, (u8*)"Status: ADC3 Sampling...");

                // b. ����ADC3����
                ADC3_Start_Sampling();

                // c. �л�����ADC3����״̬
                advanced_ui_state = UI_STATE_ADC3_SAMPLING;
            }
            break;

        // ״̬2: ɨƵ��
        case UI_STATE_SWEEPING:
            if (!DSP_Is_Sweeping()) { // �ȴ�ɨƵ���
                // a. ɨƵ��ɣ��ٴθ�����ʱ����
                LCD_Fill(0, 250, 239, 319, WHITE);
                LCD_ShowString(20, 250, 200, 16, 16, (u8*)"Status: Analyzing...");
                
                // b. �л�������״̬
                advanced_ui_state = UI_STATE_ANALYZING;
            }
            break;

        // ��������״̬3: ������
        case UI_STATE_ANALYZING:
            // a. ����ѭ���а�ȫ�ص��÷�������
            //    ������̷ǳ��죬������˲�����
            Analyze_Sweep_Result(NUM_SWEEP_POINTS);
            
            // b. ������ɺ�����������ʾ״̬
            advanced_ui_state = UI_STATE_DISPLAYING;
            break;

        // ״̬4: ��ʾ��� (ԭ״̬3)
        case UI_STATE_DISPLAYING:
            // a. ��ȡ�ѷ����õĽ��
            learned_type = DSP_Get_Learned_Type();
            
            // b. ������Ļ��׼����ʾ
            LCD_Fill(0, 250, 239, 319, WHITE);
            
            // c. ���߼���ȫ����ʾ�˲������ͣ������˶Դ���Ĵ���
            sprintf(buffer, "Type: ");
            switch(learned_type) {
                case TYPE_LOW_PASS:  strcat(buffer, "Low-Pass");  break;
                case TYPE_HIGH_PASS: strcat(buffer, "High-Pass"); break;
                case TYPE_BAND_PASS: strcat(buffer, "Band-Pass"); break;
                case TYPE_BAND_STOP: strcat(buffer, "Band-Stop"); break; // <-- �Ѳ�ȫ
                default:             strcat(buffer, "Unknown");   break;
            }
            LCD_ShowString(20, 250, 220, 16, 16, (u8*)buffer);

            // d. ���߼���ȫ������������ʾ��ͬ�Ĺؼ��������ϲ��˴�ͨ�ʹ�����߼�
            if (learned_type == TYPE_LOW_PASS || learned_type == TYPE_HIGH_PASS) {
                sprintf(buffer, "Fc: %.0f Hz", DSP_Get_Cutoff_Freq1());
                LCD_ShowString(20, 270, 220, 16, 16, (u8*)buffer);
            } 
            else if (learned_type == TYPE_BAND_PASS || learned_type == TYPE_BAND_STOP) { // <-- �ϲ��߼�
                sprintf(buffer, "Fcen: %.0f Hz", DSP_Get_Center_Freq());
                LCD_ShowString(20, 270, 220, 16, 16, (u8*)buffer);
                
                sprintf(buffer, "BW: %.0f Hz, Q: %.2f", DSP_Get_Bandwidth(), DSP_Get_Q_Factor());
                LCD_ShowString(20, 290, 220, 16, 16, (u8*)buffer);
            }
            
            // e. ��ʾ��ϣ����ؿ���״̬��׼����һ�β���
            advanced_ui_state = UI_STATE_IDLE;
            break;

        // ״̬5: ADC3����
        case UI_STATE_ADC3_SAMPLING:
            // ���ADC3�����Ƿ����
            if (adc3_capture_done > 0) {
                // a. ֹͣADC3����
                ADC3_Stop_Sampling();

                // b. ������Ļ��ʾ����ɹ�
                LCD_Fill(0, 250, 239, 319, WHITE);
                sprintf(buffer, "ADC3 Sampling Complete! Samples: %d", ADC3_BUF_SIZE);
                LCD_ShowString(20, 250, 220, 16, 16, (u8*)buffer);

                // c. ��ʾ����Ƶ��
                sprintf(buffer, "Sample Rate: %d Hz", ADC3_SAM_FRE);
                LCD_ShowString(20, 270, 220, 16, 16, (u8*)buffer);

                // d. ��ʾ����ͨ��
                LCD_ShowString(20, 290, 220, 16, 16, (u8*)"Channel: PA7 (ADC3_IN7)");

                // e. ����ؿ���״̬
                advanced_ui_state = UI_STATE_IDLE;
            }
            break;
    }
}

/**
 * @brief  Draws the static UI frame for Advanced Mode.
 */
static void UI_Draw_Advanced_Mode_Screen(void)
{
    // --- ��1�������������Ļ������һ���ɾ��Ļ��� ---
    LCD_Clear(WHITE);

    // --- ��2�������ƾ�̬�ġ���ײ��Ԫ�� ---
    
    // a) ���Ʊ����� (��ɫ)
    POINT_COLOR = WHITE; 
    BACK_COLOR = GREEN;
    LCD_Fill(0, 0, lcddev.width - 1, 29, GREEN);
    LCD_ShowString(70, 8, 240, 16, 16, (u8*)"Advanced Mode");

    // b) ���Ƶײ���״̬������
    POINT_COLOR = BLACK; 
    BACK_COLOR = WHITE;
    LCD_DrawLine(0, 240, 239, 240);
    LCD_ShowString(20, 250, 200, 16, 16, (u8*)"Status: Idle");

    // --- ��3�����ھ�̬����֮�ϣ����ƿɽ����İ�ť ---
    //    ȷ���ڻ��ư�ťʱ��ʹ����ȷ����ɫ
    
    // a) ���� "Learn & Model" ��ť
    POINT_COLOR = GREEN; // ��ť�߿��������ɫ
    BACK_COLOR = WHITE;      // ���ֵı���ɫ
    LCD_DrawRectangle(btn_learn.x, btn_learn.y, btn_learn.x + btn_learn.w, btn_learn.y + btn_learn.h);
    LCD_ShowString(btn_learn.x + 30, btn_learn.y + 18, 150, 16, 16, (u8*)btn_learn.text);

    // b) ���� "Reproduce Signal" ��ť
    LCD_DrawRectangle(btn_reproduce.x, btn_reproduce.y, btn_reproduce.x + btn_reproduce.w, btn_reproduce.y + btn_reproduce.h);
    LCD_ShowString(btn_reproduce.x + 25, btn_reproduce.y + 18, 150, 16, 16, (u8*)btn_reproduce.text);
}
/**
 * @brief  Scans and identifies which button is pressed in Advanced Mode.
 */
static uint8_t UI_Get_Button_Pressed_Advanced(void)
{
    TP_Scan(0);
    if (tp_dev.sta & TP_PRES_DOWN) {
        uint16_t x = tp_dev.x[0], y = tp_dev.y[0];
        if (x > btn_learn.x && x < (btn_learn.x+btn_learn.w) && y > btn_learn.y && y < (btn_learn.y+btn_learn.h)) return 1;
        if (x > btn_reproduce.x && x < (btn_reproduce.x+btn_reproduce.w) && y > btn_reproduce.y && y < (btn_reproduce.y+btn_reproduce.h)) return 2;
    }
    return 0; 
}
